import 'package:cliente_minha_unimed/shared/api/vo/authorization.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AcceptedState extends Equatable {
  const AcceptedState();

  @override
  List<Object?> get props => [];
}

class AcceptedInitial extends AcceptedState {}

class NoRequestState extends AcceptedState {
  @override
  List<Object> get props => [];
}

class RequestAccepted extends AcceptedState {
  final EnvioSolicitacaoAutorizacao? envioSolicitacaoAutorizacao;
  @override
  List<Object?> get props => [envioSolicitacaoAutorizacao];
  RequestAccepted({this.envioSolicitacaoAutorizacao});
}
