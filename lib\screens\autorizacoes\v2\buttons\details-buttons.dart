import 'package:cliente_minha_unimed/bloc/beneficiary-protocol/beneficiary_protocol_detail_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v2/card-detail.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v2/card-guide.dart';
import 'package:cliente_minha_unimed/shared/i18n/i18n_helper.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class DetailsButton extends StatelessWidget {
  final BeneficiaryProtocolModel beneficiaryProtocolModel;

  DetailsButton({required this.beneficiaryProtocolModel});

  final String baseTranslate = 'autorizations.v2.card.buttons';

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<BeneficiaryProtocolDetailBloc,
        BeneficiaryProtocolDetailState>(
      listener: (context, state) {
        if (state is LoadedBeneficiaryProtocolDetailState) {
          Navigator.push(context,
              new CardDetailDialogRoute(builder: (BuildContext context) {
            return Scaffold(
              backgroundColor: Colors.black45,
              body: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Center(
                  child: Hero(
                    tag:
                        "card-${beneficiaryProtocolModel.ansProtocolNumberHistory.ansProtocolNumber}",
                    child: BeneficiaryProtocolCardGuide(
                      beneficiaryProtocol: beneficiaryProtocolModel,
                      beneficiaryProtocolDetail: state.detail,
                    ),
                  ),
                ),
              ),
            );
          }));
        } else if (state is ErrorBeneficiaryProtocolDetailState) {
          Alert.open(context, title: "Erro", text: state.message);
        }
      },
      builder: (context, state) {
        return Row(
          children: [
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: UnimedColors.greenDark2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    side: BorderSide(color: UnimedColors.greenDark4)),
                onPressed: () {
                  if (state is LoadingBeneficiaryProtocolDetailState) return;
                  BlocProvider.of<BeneficiaryProtocolDetailBloc>(context).add(
                      GetBeneficiaryProtocolDetail(
                          beneficiaryProtocol: beneficiaryProtocolModel));
                },
                child: Padding(
                  padding: const EdgeInsets.only(
                      top: 8.0, bottom: 8.0, left: 24.0, right: 24.0),
                  child: state is LoadingBeneficiaryProtocolDetailState
                      ? SpinKitThreeBounce(
                          color: UnimedColors.greenDark,
                          size: 16,
                        )
                      : Text(
                          I18nHelper.translate(
                              context, '$baseTranslate.details'),
                          style: TextStyle(
                            color: UnimedColors.greenDark,
                          )),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
