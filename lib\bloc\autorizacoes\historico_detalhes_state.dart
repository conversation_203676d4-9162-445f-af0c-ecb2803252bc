import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/solicitacao-autorizacao.model.dart';

@immutable
abstract class AutorizacoesHistoricoDetalhesState extends Equatable {}

class InitialDetalhesState extends AutorizacoesHistoricoDetalhesState {
  @override
  List<Object> get props => [];
}

class LoadingDetalhesState extends AutorizacoesHistoricoDetalhesState {
  final SolicitacaoAutorizacao solicitacao;

  @override
  List<Object> get props => [solicitacao];

  LoadingDetalhesState(this.solicitacao);
}

class ErrorDetalhesState extends AutorizacoesHistoricoDetalhesState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorDetalhesState(this.message);
}

class DoneDetalhesState extends AutorizacoesHistoricoDetalhesState {
  final SolicitacaoAutorizacao solicitacao;
  final String detalhe;

  @override
  List<Object> get props => [solicitacao, detalhe];

  DoneDetalhesState(this.solicitacao, this.detalhe);
}
