import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/indica-adesao-notificacao.vo.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'accession-notification-event.dart';
import 'accession-notification-state.dart';

class AccessionNotificationBloc
    extends Bloc<AccessionNotificationEvent, AccessionNotificationState> {
  AccessionNotificationBloc() : super(InitialState());
  final logger = UnimedLogger(className: 'CadastroBloc');

  late IndicaAdesaoNotificacaoVO _indicaAdesaoNotificacaoVO;

  IndicaAdesaoNotificacaoVO get indicaAdesaoNotificacaoVO =>
      _indicaAdesaoNotificacaoVO;

  @override
  Stream<AccessionNotificationState> mapEventToState(
    AccessionNotificationEvent event,
  ) async* {
    if (event is CheckAccessionNotificationEvent) {
      yield LoadingCheckCadastralState();
      try {
        final api = Locator.instance.get<BeneficiarioApi>();

        _indicaAdesaoNotificacaoVO =
            await api.checkAccessionNotification(event.perfil, event.cpf);

        if (_indicaAdesaoNotificacaoVO.sucesso ?? false) {
          yield ShowAccessionNotificationState();
        } else {
          yield DontShowAccessionNotificationState();
        }
      } catch (e) {
        yield DontShowAccessionNotificationState();
      }
    } else if (event is SetToInitialAccessionNotificationEvent) {
      yield InitialState();
    } else if (event is RegisterUpdateEvent) {
      try {
        yield LoadingCheckCadastralState();

        final api = Locator.instance.get<BeneficiarioApi>();

        final success = await api.registerContatcAccession(
          perfil: event.perfil,
          cpf: event.cpf,
          email: event.email,
          data: event.data,
          selectedType: event.selectedType,
        );

        if (success) {
          yield SuccessRegisteradastralUpdateState();
        } else {
          ErrorRegisteradastralUpdateState(message: MessageException.GENERAL);
        }
      } catch (e) {
        yield ErrorRegisteradastralUpdateState(message: e.toString());
      }
    }
  }
}
