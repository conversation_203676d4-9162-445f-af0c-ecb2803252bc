part of 'my_data_bloc.dart';

abstract class CheckinLaboratoryMyDataState extends Equatable {
  const CheckinLaboratoryMyDataState();

  @override
  List<Object?> get props => [];
}

class CheckinLaboratoryMyDataInitialState
    extends CheckinLaboratoryMyDataState {}

class LoadingCheckinLaboratoryMyDataState
    extends CheckinLaboratoryMyDataState {}

class SucessLoadedCheckinLabState extends CheckinLaboratoryMyDataState {
  final CheckinLabModel? checkinLabModel;

  @override
  List<Object?> get props => [checkinLabModel];

  SucessLoadedCheckinLabState({this.checkinLabModel});
}

class ErrorCheckinLabState extends CheckinLaboratoryMyDataState {
  final String message;

  @override
  List<Object?> get props => [message];

  ErrorCheckinLabState({required this.message});
}

class LoadedCheckinLaboratoryMyDataState extends CheckinLaboratoryMyDataState {
  final String email;
  final String telefone;
  @override
  List<Object?> get props => [email, telefone];

  LoadedCheckinLaboratoryMyDataState(
      {required this.email, required this.telefone});
}
