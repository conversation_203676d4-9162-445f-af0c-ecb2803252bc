import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/especialidade-agendamento.vo.dart';

@immutable
abstract class EspecialidadeState extends Equatable {}

class EspecialidadeInitialState extends EspecialidadeState {
  @override
  List<Object> get props => [];
}

class EspecialidadeLoadingState extends EspecialidadeState {
  @override
  List<Object> get props => [];
}

class EspecialidadeErrorState extends EspecialidadeState {
  final String message;

  @override
  List<Object> get props => [message];

  EspecialidadeErrorState(this.message);
}

class EspecialidadeDoneState extends EspecialidadeState {
  final Iterable<EspecialidadeAgendamento> especialidades;

  @override
  List<Object> get props => [especialidades];

  EspecialidadeDoneState(this.especialidades);
}

class DoneSelectEspecialidadeState extends EspecialidadeState {
  final EspecialidadeAgendamento? especialidade;

  @override
  List<Object?> get props => [especialidade];

  DoneSelectEspecialidadeState(this.especialidade);
}

class LoadingEspecialitiesState extends EspecialidadeState {
  @override
  List<Object> get props => [];
}

class LoadedEspecialitiesState extends EspecialidadeState {
  final Iterable<EspecialtyModel> especialities;

  @override
  List<Object> get props => [especialities];

  LoadedEspecialitiesState(this.especialities);
}

class ErrorEspecialitiesState extends EspecialidadeState {
  final String? message;
  @override
  List<Object?> get props => [message];

  ErrorEspecialitiesState(this.message);
}

class EspecialitiesNoDataState extends EspecialidadeState {
  @override
  List<Object> get props => [];
}
