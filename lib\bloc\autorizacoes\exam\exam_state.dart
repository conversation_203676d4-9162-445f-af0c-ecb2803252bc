import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';

abstract class ExamState extends Equatable {
  const ExamState();

  @override
  List<Object?> get props => [];
}

class ExamInitial extends ExamState {}

class LoadingListExamState extends ExamState {
  @override
  List<Object> get props => [];
}

class ErrorListExamState extends ExamState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorListExamState(this.message);
}

class DoneListExamState extends ExamState {
  final List<ElectronicGuide>? exams;

  @override
  List<Object?> get props => [exams];

  DoneListExamState({this.exams});
}

class NoDataExamState extends ExamState {
  @override
  List<Object> get props => [];
}

class LoadingGetDetails extends ExamState {
  @override
  List<Object> get props => [];
}

class ErrorGetDetails extends ExamState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorGetDetails(this.message);
}

class DoneGetDetails extends ExamState {
  final ElectronicGuide electronicGuide;

  @override
  List<Object?> get props => [electronicGuide];

  DoneGetDetails({required this.electronicGuide});
}

class LoadingPdf extends ExamState {
  @override
  List<Object> get props => [];
}

class ErrorPdf extends ExamState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorPdf(this.message);
}

class DonePdf extends ExamState {
  final String path;
  final int? codSolicitacao;

  @override
  List<Object?> get props => [path, codSolicitacao];

  DonePdf({required this.path, required this.codSolicitacao});
}
