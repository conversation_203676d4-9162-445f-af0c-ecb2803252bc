part of 'guide_bloc.dart';

abstract class GuideState extends Equatable {
  const GuideState();

  @override
  List<Object> get props => [];
}

class GuideInitial extends GuideState {}

class LoadingListGuide extends GuideState {}

class SuccessListGuide extends GuideState {
  final List<GuideModel>? guides;
  SuccessListGuide({this.guides});
}

class ErrorListGuide extends GuideState {
  final String? message;
  ErrorListGuide({this.message});
}

class NoDataListGuide extends GuideState {
  final String? message;
  NoDataListGuide({this.message});
}
