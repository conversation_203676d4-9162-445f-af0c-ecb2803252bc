import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:evaluation/evaluation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'avaliacao_event.dart';
import 'avaliacao_state.dart';

class EvaluationBloc extends Bloc<EvaluationEvent, EvaluationState> {
  EvaluationBloc() : super(InitialState());
  final logger = UnimedLogger(className: 'EvaluationBloc');
  bool? _success = false;

  @override
  Stream<EvaluationState> mapEventToState(
    EvaluationEvent event,
  ) async* {
    if (event is SetSuccessEvaluationEvent) {
      _success = event.success;
    } else if (event is CheckEvaluationEvent) {
      yield LoadingState();

      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? lastTime = prefs.getString('lastTimeDate');

      if (event.enable! &&
          _success! &&
          (lastTime == null || calulateDifference(lastTime) > event.minutes!)) {
        _success = false;
        prefs.setString('lastTimeDate', DateTime.now().toString());
        yield NeededShowEvaluationState();
      } else
        yield NoNeedShowEvaluationState();
    } else if (event is SendEvaluationEvent) {
      final _evaluation = Locator.instance.get<Evaluation>();

      String? token = "";
      if (event.sendPerfilApps!)
        token = await (Locator.instance.get<AuthApi>().tokenPerfilApps());

      String? mensagem = await _evaluation.evaluate(
        stars: event.stars!,
        comment: event.comment!,
        servico: event.servico!,
        sendPerfilApps: event.sendPerfilApps!,
        atendimento: event.atendimento.toString(),
        token: token,
        typePa: event.typePa,
      );

      logger.d('EvaluationBloc SendEvaluationEvent $mensagem');
    }
  }

  int calulateDifference(String date) {
    DateTime dateTime = DateTime.parse(date);

    Duration difference = DateTime.now().difference(dateTime);

    return difference.inMinutes;
  }
}
