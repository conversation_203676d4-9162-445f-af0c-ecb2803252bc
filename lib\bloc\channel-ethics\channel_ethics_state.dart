import 'package:cliente_minha_unimed/models/channel-ethics/chanel-ethics.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class ChannelEthicsState extends Equatable {}

class InitialChannelEthicsState extends ChannelEthicsState {
  @override
  List<Object> get props => [];
}

class LoadingChannelEthicsState extends ChannelEthicsState {
  @override
  List<Object> get props => [];
}

class ErrorChannelEthicsState extends ChannelEthicsState {
  final String message;
  @override
  List<Object> get props => [message];
  ErrorChannelEthicsState(this.message);
}

class DoneChannelEthicsState extends ChannelEthicsState {
  final ChannelEthicsModel channelEthicsModel;

  @override
  List<Object> get props => [];
  DoneChannelEthicsState({required this.channelEthicsModel});
}
