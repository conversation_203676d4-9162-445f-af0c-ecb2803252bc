import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/agenda-agendamento.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';

@immutable
abstract class ConsultaState extends Equatable {}

class ConsultaInitialState extends ConsultaState {
  @override
  List<Object> get props => [];
}

// Lista de prestadores
class ConsultaListPrestadorLoadingState extends ConsultaState {
  @override
  List<Object> get props => [];
}

class ConsultaListPrestadorErrorState extends ConsultaState {
  final String message;

  @override
  List<Object> get props => [message];

  ConsultaListPrestadorErrorState(this.message);
}

class ConsultaListPrestadorDoneState extends ConsultaState {
  final Iterable<ProviderModel> prestadores;

  @override
  List<Object> get props => [prestadores];

  ConsultaListPrestadorDoneState(this.prestadores);
}

class ConsultaListPrestadorNoDataState extends ConsultaState {
  @override
  List<Object> get props => [];
}

// Agendas do Prestador
class ConsultaAgendaPrestadorLoadingState extends ConsultaState {
  ConsultaAgendaPrestadorLoadingState(this.month);
  final int month;
  @override
  List<Object> get props => [];
}

class ConsultaAgendaPrestadorErrorState extends ConsultaState {
  final String message;
  final DateTime requestedDate;

  @override
  List<Object> get props => [message];

  ConsultaAgendaPrestadorErrorState(this.message, this.requestedDate);
}

class ConsultaAgendaPrestadorDoneState extends ConsultaState {
  final Iterable<AgendaAgendamentoVO> agendas;
  final DateTime dtInicio;

  @override
  List<Object> get props => [agendas];

  ConsultaAgendaPrestadorDoneState(this.agendas, this.dtInicio);
}

class ConsultaAgendaPrestadorNoDataState extends ConsultaState {
  ConsultaAgendaPrestadorNoDataState(this.requestTime);
  final DateTime requestTime;
  @override
  List<Object> get props => [];
}

class AdvancedStepState extends ConsultaState {
  final int toIndex;

  @override
  List<Object> get props => [toIndex];

  AdvancedStepState({required this.toIndex});
}

class BackedStepState extends ConsultaState {
  @override
  List<Object?> get props => [];
}
