part of 'checkin_page_info_bloc.dart';

abstract class CheckinInfoState extends Equatable {
  const CheckinInfoState();

  @override
  List<Object?> get props => [];
}

class CheckinInfoInitial extends CheckinInfoState {}

class LoadingCheckinInfoState extends CheckinInfoState {}

class ErrorCheckinInfoState extends CheckinInfoState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorCheckinInfoState(this.message);
}

class LoadedCheckinInfoState extends CheckinInfoState {
  final CheckinDataModel? checkinData;

  @override
  List<Object?> get props => [checkinData];

  LoadedCheckinInfoState({this.checkinData});
}

class SucessLoadedCheckinState extends CheckinInfoState {
  final CheckinDataModel? checkinData;

  @override
  List<Object?> get props => [checkinData];

  SucessLoadedCheckinState({this.checkinData});
}
