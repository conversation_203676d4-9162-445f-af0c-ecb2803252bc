import 'package:cliente_minha_unimed/models/medical-guide/provider-solicitation.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class ProviderSolicitationState extends Equatable {}

class ProviderSolicitationInitialState extends ProviderSolicitationState {
  @override
  List<Object> get props => [];
}

class ProviderSolicitationLoadingState extends ProviderSolicitationState {
  @override
  List<Object> get props => [];
}

class ProviderSolicitationErrorState extends ProviderSolicitationState {
  final String message;

  @override
  List<Object> get props => [message];

  ProviderSolicitationErrorState(this.message);
}

class ProviderSolicitationDoneState extends ProviderSolicitationState {
  final Iterable<ProviderSolicitationModel> prestadores;

  @override
  List<Object> get props => [prestadores];

  ProviderSolicitationDoneState(this.prestadores);
}

class DoneSelectProviderSolicitationState extends ProviderSolicitationState {
  final ProviderSolicitationModel? providerSolicitation;

  @override
  List<Object?> get props => [providerSolicitation];

  DoneSelectProviderSolicitationState(this.providerSolicitation);
}
