import 'package:cliente_minha_unimed/models/document-required.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AuthorizationDocumentsState extends Equatable {}

class InitialState extends AuthorizationDocumentsState {
  @override
  List<Object> get props => [];
}

class LoadingState extends AuthorizationDocumentsState {
  @override
  List<Object> get props => [];
}

class LoadingDocumentState extends AuthorizationDocumentsState {
  final String message;
  final int index;

  @override
  List<Object> get props => [index, message];

  LoadingDocumentState({required this.index, required this.message});
}

class LoadingBase64State extends AuthorizationDocumentsState {
  final int index;

  @override
  List<Object> get props => [index];

  LoadingBase64State({required this.index});
}

class FileSendedState extends AuthorizationDocumentsState {
  final int index;

  @override
  List<Object> get props => [index];

  FileSendedState({required this.index});
}

class ErrorFileSendedState extends AuthorizationDocumentsState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorFileSendedState({required this.message});
}

class ErrorFileLoadingState extends AuthorizationDocumentsState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorFileLoadingState({required this.message});
}

class ErrorSendedState extends AuthorizationDocumentsState {
  final int index;

  @override
  List<Object> get props => [index];

  ErrorSendedState({required this.index});
}

class FinishSendAllFileState extends AuthorizationDocumentsState {
  final List<DocumentRequired> attachments;

  @override
  List<Object> get props => [attachments];

  FinishSendAllFileState({required this.attachments});
}

class ErrorState extends AuthorizationDocumentsState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorState(this.message);
}

class DoneState extends AuthorizationDocumentsState {
  final List<DocumentRequired> attachments;

  @override
  List<Object> get props => [attachments];

  DoneState(this.attachments);
}

class AutorizacaoAttachmentErrorState extends AuthorizationDocumentsState {
  final message;
  AutorizacaoAttachmentErrorState({@required this.message});
  @override
  List<Object> get props => [this.message];
}
