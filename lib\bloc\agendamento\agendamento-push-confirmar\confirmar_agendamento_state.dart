import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class ConfirmarAgendamentoState extends Equatable {}

class ConfirmarAgendamentoInitialState extends ConfirmarAgendamentoState {
  @override
  List<Object> get props => [];
}

class ConfirmarAgendamentoLoadingState extends ConfirmarAgendamentoState {
  @override
  List<Object> get props => [];
}

class ConfirmarAgendamentoErrorState extends ConfirmarAgendamentoState {
  final String message;

  @override
  List<Object> get props => [message];

  ConfirmarAgendamentoErrorState(this.message);
}

class ConfirmarAgendamentoDoneState extends ConfirmarAgendamentoState {
   final String message;

  @override
  List<Object> get props => [];
  ConfirmarAgendamentoDoneState(this.message);
}
