import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/solicitacoes.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:intl/intl.dart';

part 'guias_event.dart';
part 'guias_state.dart';

class GuiasBloc extends Bloc<GuiasEvent, GuiasState> {
  GuiasBloc() : super(GuiasInitial());

  Map<String?, List<GuiaModel>> _status = Map<String?, List<GuiaModel>>();

  List<GuiaModel> _solicitacoes = List.empty(growable: true);
  List<GuiaModel> _solicitacoesFull = List.empty(growable: true);

  @override
  Stream<GuiasState> mapEventToState(
    GuiasEvent event,
  ) async* {
    if (event is CarregarGuiasEvent) {
      yield LoadingCarregarGuiaState();

      try {
        _solicitacoesFull =
            await Locator.instance.get<SolicitacoesApi>().carregarGuias(
                  perfil: event.perfil,
                  dataInicio: event.dataInicio,
                  dataFim: event.dataFim,
                );
        if (_solicitacoesFull.length > 0) {
          _handleByStatus(_solicitacoesFull);
          yield SuccessCarregarGuiaState(
            list: _solicitacoesFull,
            listStatus: _status,
          );
        } else {
          yield NoDataGuiaState();
        }
      } catch (ex) {
        yield ErrorCarregarGuiaState(message: ex.toString());
      }
    } else if (event is GuiasFiltro) {
      yield LoadingCarregarGuiaState();
      if (event.filter != null) {
        if (event.filter!.isEmpty) {
          _solicitacoes = _solicitacoesFull;
        } else {
          _solicitacoes = List<GuiaModel>.empty(growable: true);
          _solicitacoesFull.forEach((solic) {
            if (solic
                .toJson()
                .toString()
                .toLowerCase()
                .contains(event.filter!.toLowerCase())) {
              if (event.dateFilter == null) {
                _solicitacoes.add(solic);
              } else {
                final dataFormatted =
                    DateFormat("dd/MM/yyyy").parse(solic.dataFormatted);
                if (dataFormatted.isAtSameMomentAs(event.dateFilter!) ||
                    dataFormatted.isAfter(event.dateFilter!))
                  _solicitacoes.add(solic);
              }
            }
          });
        }
      }
      if (event.dateFilter != null) {
        _solicitacoes = List<GuiaModel>.empty(growable: true);
        _solicitacoesFull.forEach((solic) {
          final dataFormatted =
              DateFormat("dd/MM/yyyy").parse(solic.dataFormatted);
          if (dataFormatted.isAtSameMomentAs(event.dateFilter!) ||
              dataFormatted.isAfter(event.dateFilter!)) {
            if (event.filter == null) {
              _solicitacoes.add(solic);
            } else {
              if (solic
                  .toJson()
                  .toString()
                  .toLowerCase()
                  .contains(event.filter!.toLowerCase()))
                _solicitacoes.add(solic);
            }
          }
        });
      }
      if (_solicitacoes.isEmpty) {
        yield NoDataFoundGuiaState();
      } else {
        _handleByStatus(_solicitacoes);
        yield SuccessCarregarGuiaState(
          list: _solicitacoesFull,
          listStatus: _status,
        );
      }
    }
  }

  void _handleByStatus(List<GuiaModel>? autorizacoes) {
    _status = Map<String?, List<GuiaModel>>();

    if (autorizacoes != null && autorizacoes.length > 0) {
      autorizacoes.forEach((autorizacao) {
        final _key = autorizacao.statusGuia;
        if (_status.containsKey(_key)) {
          _status[_key]!.add(autorizacao);
        } else {
          _status[_key] = List<GuiaModel>.empty(growable: true)
            ..add(autorizacao);
        }
      });
    }
  }
}
