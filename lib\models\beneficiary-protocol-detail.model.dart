import 'package:intl/intl.dart';

class BeneficiaryProtocolDetailModel {
  String? details;
  String? deadline;
  List<Appointment>? appointments;

  BeneficiaryProtocolDetailModel(
      {required this.details,
      required this.deadline,
      required this.appointments});

  String? get deadlineFormated {
    try {
      return deadline != null
          ? DateFormat("dd/MM/yyyy").format(DateTime.parse(this.deadline ?? ''))
          : null;
    } catch (e) {
      return this.deadline;
    }
  }

  BeneficiaryProtocolDetailModel.fromJson(Map<String, dynamic> json) {
    details = json['details'];
    deadline = json['deadline'];
    if (json['appointments'] != null) {
      appointments = <Appointment>[];
      json['appointments'].forEach((v) {
        appointments!.add(Appointment.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['details'] = this.details;
    data['deadline'] = this.deadline;
    if (this.appointments != null) {
      data['appointments'] = this.appointments!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Appointment {
  String? appointmentDateTime;
  String? specialty;
  String? location;
  String? doctor;

  Appointment(
      {this.appointmentDateTime, this.specialty, this.location, this.doctor});

  String? get appointmentDateTimeFormated {
    try {
      return appointmentDateTime != null
          ? DateFormat("dd/MM/yyyy HH:mm")
              .format(DateTime.parse(this.appointmentDateTime ?? ''))
          : null;
    } catch (e) {
      return this.appointmentDateTime;
    }
  }

  Appointment.fromJson(Map<String, dynamic> json) {
    appointmentDateTime = json['appointmentDateTime'];
    specialty = json['specialty'];
    location = json['location'];
    doctor = json['doctor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['appointmentDateTime'] = this.appointmentDateTime;
    data['specialty'] = this.specialty;
    data['location'] = this.location;
    data['doctor'] = this.doctor;
    return data;
  }
}
