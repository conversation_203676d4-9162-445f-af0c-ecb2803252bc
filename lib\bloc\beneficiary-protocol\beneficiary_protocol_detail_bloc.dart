import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol-detail.model.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol.model.dart';
import 'package:cliente_minha_unimed/shared/api/protocols.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'beneficiary_protocol_detail_event.dart';
part 'beneficiary_protocol_detail_state.dart';

class BeneficiaryProtocolDetailBloc extends Bloc<BeneficiaryProtocolDetailEvent,
    BeneficiaryProtocolDetailState> {
  BeneficiaryProtocolDetailBloc() : super(BeneficiaryProtocolDetailInitial());

  late BeneficiaryProtocolDetailModel _detail;

  @override
  Stream<BeneficiaryProtocolDetailState> mapEventToState(
    BeneficiaryProtocolDetailEvent event,
  ) async* {
    if (event is GetBeneficiaryProtocolDetail) {
      yield LoadingBeneficiaryProtocolDetailState();

      try {
        _detail = await Locator.instance
            .get<BeneficiaryProtocolApi>()
            .getBeneficiaryProtocolDetails(
                beneficiaryProtocol: event.beneficiaryProtocol);

        yield LoadedBeneficiaryProtocolDetailState(detail: _detail);
      } catch (ex) {
        yield ErrorBeneficiaryProtocolDetailState(message: ex.toString());
      }
    }
  }
}
