import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-lab/checkin-lab.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'create_new_event.dart';
part 'create_new_state.dart';

class CreateNewBloc extends Bloc<CreateNewEvent, CreateNewState> {
  CreateNewBloc() : super(CreateNewInitial());
  @override
  Stream<CreateNewState> mapEventToState(
    CreateNewEvent event,
  ) async* {
    if (event is DeleteQrCodeEvent) {
      yield LoadingDeleteQrCodeState();

      try {
        await Locator.instance.get<CheckinLabApi>().deleteQrCode(
              perfil: event.perfil,
            );

        yield SucessDeleteQrCodeState();
      } catch (ex) {
        yield ErrorDeleteQrCodeState(message: ex.toString());
      }
    }
  }
}
