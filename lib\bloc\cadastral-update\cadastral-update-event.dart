part of 'cadastral-update-bloc.dart';

abstract class CadastralUpdateEvent extends Equatable {
  const CadastralUpdateEvent();

  @override
  List<Object?> get props => [];
}

class VerifyTimePA extends CadastralUpdateEvent {
  final Carteira carteira;

  @override
  List<Object> get props => [carteira];

  VerifyTimePA(this.carteira);
}

class AdvanceStepCadastralUpdateEvent extends CadastralUpdateEvent {
  AdvanceStepCadastralUpdateEvent();
}

class BackStepCadastralUpdateEvent extends CadastralUpdateEvent {
  BackStepCadastralUpdateEvent();
}

class UpdateDataCadastralUpdate extends CadastralUpdateEvent {
  final String data;
  final String email;
  final String selectedType;

  @override
  List<Object?> get props => [data, email, selectedType];

  UpdateDataCadastralUpdate(
      {required this.data, required this.email, required this.selectedType});
}
