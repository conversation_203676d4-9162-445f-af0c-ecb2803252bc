import 'package:cliente_minha_unimed/shared/api/vo/authorization.vo.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class VerifySolicState extends Equatable {}

class VerifySolicInitial extends VerifySolicState {
  @override
  List<Object> get props => [];
}

class HaveRequestState extends VerifySolicState {
  final EnvioSolicitacaoAutorizacao? envioSolicitacaoAutorizacao;
  @override
  List<Object?> get props => [envioSolicitacaoAutorizacao];
  HaveRequestState({this.envioSolicitacaoAutorizacao});
}

class NoRequestState extends VerifySolicState {
  @override
  List<Object> get props => [];
}

class LoadingRequest extends VerifySolicState {
  @override
  List<Object> get props => [];
}

class VerifyingRequest extends VerifySolicState {
  @override
  List<Object> get props => [];
}

class SuccessRequest extends VerifySolicState {
  @override
  List<Object> get props => [];
}

class FailRequest extends VerifySolicState {
  final EnvioSolicitacaoAutorizacao? envioSolicitacaoAutorizacao;
  @override
  List<Object?> get props => [envioSolicitacaoAutorizacao];
  FailRequest({this.envioSolicitacaoAutorizacao});
}
