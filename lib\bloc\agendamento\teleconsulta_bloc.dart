import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/teleconsulta_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/teleconsulta_state.dart';
import 'package:cliente_minha_unimed/models/agenda-agendamento.model.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/teleconsulta.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/autorizacao-teleconsulta.vo.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';

class TeleconsultaBloc extends Bloc<TeleconsultaEvent, TeleconsultaState> {
  TeleconsultaBloc() : super(TeleconsultaInitialState());

  final logger = UnimedLogger(className: 'TeleconsultaBloc');

  int _pageIndex = 0;
  int get pageIndex => _pageIndex;

  EspecialtyModel? _especialidadeSelected;
  EspecialtyModel? get especialidadeSelected => _especialidadeSelected;

  AgendaAgendamentoVO? _horarioSelected;
  AgendaAgendamentoVO? get horarioSelected => _horarioSelected;

  @override
  Stream<TeleconsultaState> mapEventToState(
    TeleconsultaEvent event,
  ) async* {
    if (event is AdvanceStep) {
      event.indexPage < 0 ? _pageIndex++ : _pageIndex = event.indexPage;

      yield AdvancedStepState(toIndex: _pageIndex);

      debugPrint('=====>>>>> advanceStep');

      // yield _handleSubtitle(_pageIndex);
    } else if (event is BackStep) {
      event.indexPage < 0 ? _pageIndex-- : _pageIndex = event.indexPage;

      debugPrint('=====>>>>> backStep');

      yield BackedStepState();
      // yield _handleSubtitle(_pageIndex);
    } else if (event is ListAllAgendasEvent) {
      yield TeleconsultaAgendaLoadingState(
        event.dataInicio?.month ?? DateTime.now().month,
      );

      final api = Locator.instance.get<TeleconsultaApi>();

      try {
        final _agendas = await api.listAgendasLivresByPrestador(
            perfil: event.perfil!,
            prestador: event.prestador!,
            clinicaUnimed: event.clinicaUnimed,
            especialidade: event.especialidade,
            isTeleconsulta: true,
            dataInicio: event.dataInicio,
            dataFim: event.dataFim);
        if (_agendas.length <= 0) {
          yield TeleconsultaAgendaNoDataState(event.dataInicio);
        } else {
          yield TeleconsultaAgendaDoneState(_agendas, event.dataInicio);
        }
      } on TeleconsultaException catch (e) {
        yield TeleconsultaAgendaErrorState(e.message, event.dataInicio);
      } on UnimedException catch (e) {
        yield TeleconsultaAgendaErrorState(e.message, event.dataInicio);
      } on Exception catch (e) {
        yield TeleconsultaAgendaErrorState("$e", event.dataInicio);
      }
    } else if (event is AutorizarEvent) {
      yield TeleconsultaAutorizarLoadingState(event.agendamento);

      final api = Locator.instance.get<TeleconsultaApi>();

      try {
        final response = await api.autorizarTeleconsulta(
          perfil: event.perfil,
          agendamento: event.agendamento,
        );

        if (response.autorizado! && response.entrarNaTeleconsulta) {
          yield TeleconsultaAutorizarPermitState(event.agendamento, response);
        } else {
          yield TeleconsultaAutorizarDenyState(event.agendamento, response);
        }
      } on UnimedException catch (_) {
        yield TeleconsultaAutorizarDenyState(
          event.agendamento,
          AutorizacaoTeleconsultaAgendamento(mensagem: _.message),
        );
      } on Exception catch (_) {
        yield TeleconsultaAutorizarDenyState(event.agendamento,
            AutorizacaoTeleconsultaAgendamento(mensagem: _.toString()));
      }
    } else if (event is ClearStateEvent) {
      _especialidadeSelected = null;
      _horarioSelected = null;
      yield TeleconsultaInitialState();
    } else if (event is SelectEspecialty) {
      _especialidadeSelected = event.especialtyModel;
    } else if (event is SelectHorario) {
      _horarioSelected = event.horario;
    }
  }
}
