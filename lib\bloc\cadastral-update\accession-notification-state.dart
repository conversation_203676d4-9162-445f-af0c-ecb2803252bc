import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AccessionNotificationState extends Equatable {}

class InitialState extends AccessionNotificationState {
  @override
  List<Object> get props => [];
}

class LoadingCheckAccessionNotificationState
    extends AccessionNotificationState {
  @override
  List<Object> get props => [];
}

class ShowAccessionNotificationState extends AccessionNotificationState {
  @override
  List<Object> get props => [];
}

class DontShowAccessionNotificationState extends AccessionNotificationState {
  @override
  List<Object> get props => [];
}

class LoadingCheckCadastralState extends AccessionNotificationState {
  @override
  List<Object> get props => [];
}

class SuccessRegisteradastralUpdateState extends AccessionNotificationState {
  @override
  List<Object> get props => [];
}

class ErrorRegisteradastralUpdateState extends AccessionNotificationState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorRegisteradastralUpdateState({required this.message});
}
