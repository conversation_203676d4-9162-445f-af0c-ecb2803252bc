import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-emergency/checkin-data.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-emergency/checkin_emercency.api.dart';
import 'package:cliente_minha_unimed/shared/api/pain-symptom.api.dart';
import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/api/virtual-emergency-service/virtual-emergency-service.api.dart';

part 'checkin_page_info_event.dart';
part 'checkin_page_info_state.dart';

class CheckinInfoBloc extends Bloc<CheckinInfoEvent, CheckinInfoState> {
  CheckinInfoBloc() : super(CheckinInfoInitial());
  PA? _paContants;
  PA? get paConstants => _paContants;

  PA? _paConfig;
  PA? get pa => _paConfig;

  String? _waitingTime;
  String? get waitingTime => _waitingTime;

  @override
  Stream<CheckinInfoState> mapEventToState(
    CheckinInfoEvent event,
  ) async* {
    if (event is GetInfosCheckin) {
      yield LoadingCheckinInfoState();
      try {
        _paConfig = event.user!.pa;

        CheckinDataModel? _checkinData = await Locator.instance
            .get<CheckinEmergencyApi>()
            .getCurrentCheckin(
              event.perfil!.contratoBeneficiario.carteira!.carteiraFormatada,
              event.user,
            );

        if (_checkinData != null) {
          yield SucessLoadedCheckinState(checkinData: _checkinData);
        } else {
          _paContants = event.user!.pa;

          _waitingTime = await Locator.instance
              .get<VirtualEmergencyServiceApi>()
              .waitingTime();

          final _profileContactData = await Locator.instance
              .get<BeneficiarioApi>()
              .getProfileData(event.perfil!.contratoBeneficiario.carteira!);

          final _contactsModel = await Locator.instance
              .get<BeneficiarioApi>()
              .getAllContacts(
                  codBenef: event
                      .perfil!.contratoBeneficiario.beneficiario!.codBenef);

          String? _cellNumber =
              _contactsModel.getPreferencial("CELULAR")?.contato;

          String? _email = _contactsModel.getPreferencial("E-MAIL")?.contato;

          final whatsappNumber = await Locator.instance
              .get<BeneficiarioApi>()
              .loadWhatsappNumber(event.perfil, _profileContactData.cpf);

          CheckinDataModel _checkinData = CheckinDataModel(
            profilePhone: _cellNumber ?? "",
            rg: _profileContactData.rg,
            profileWhatsapp: whatsappNumber,
            profileEmail: _email ?? "",
            symptomsDone: false,
            comorbiditiesDone: false,
            checklistPain: await Locator.instance.get<PainSymptomApi>().getList(
                  type: PainSymptomEnum.PAINS,
                  card: event.perfil!.carteira!.carteiraNumero,
                ),
            checklistSymtons: await Locator.instance
                .get<PainSymptomApi>()
                .getList(
                    type: PainSymptomEnum.SYMPTOMS,
                    card: event.perfil!.carteira!.carteiraNumero),
            checkListComorbidities: await Locator.instance
                .get<PainSymptomApi>()
                .getList(
                    type: PainSymptomEnum.COMORBIDITIES,
                    card: event.perfil!.carteira!.carteiraNumero),
          );

          yield LoadedCheckinInfoState(checkinData: _checkinData);
        }
      } catch (ex) {
        yield ErrorCheckinInfoState(ex.toString());
      }
    }
  }
}
