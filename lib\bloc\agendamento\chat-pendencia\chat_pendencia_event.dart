import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class ChatPendenciaEvent extends Equatable {}

class SalvarMensagem extends ChatPendenciaEvent {
  final String? protocolo;
  final String mensagem;

  SalvarMensagem({required this.protocolo, required this.mensagem});

  @override
  List<Object?> get props => [protocolo, mensagem];
}

class GetListChat extends ChatPendenciaEvent {
  final String? protocolo;

  GetListChat({required this.protocolo});

  @override
  List<Object?> get props => [protocolo];
}
