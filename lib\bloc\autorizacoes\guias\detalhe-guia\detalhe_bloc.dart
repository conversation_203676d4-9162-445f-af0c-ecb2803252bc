import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/solicitacoes.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'detalhe_event.dart';
part 'detalhe_state.dart';

class DetalheBloc extends Bloc<DetalheEvent, DetalheState> {
  DetalheBloc() : super(DetalheInitial());

  @override
  Stream<DetalheState> mapEventToState(
    DetalheEvent event,
  ) async* {
    if (event is CarregarDetalheEvent) {
      yield LoadingCarregarDetalheState();

      try {
        final _guia = await Locator.instance
            .get<SolicitacoesApi>()
            .carregarDetalheGuia(perfil: event.perfil, guia: event.guia);
        yield SuccessCarregarDetalheState(guia: _guia);
      } catch (ex) {
        yield ErrorCarregarDetalheState(message: ex.toString());
      }
    }
  }
}
