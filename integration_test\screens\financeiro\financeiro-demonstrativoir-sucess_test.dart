import 'package:cliente_minha_unimed/main.dart' as app;
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import '../../shared/utils/login_othercpf.dart';

Future<void> fecharPopups(WidgetTester tester) async {
  // Lista de textos comuns em popups
  final List<String> botoesPopup = [
    'Cancelar',
    '<PERSON><PERSON><PERSON>',
    'OK',
    'Entendi',
    'Voltar',
    '<PERSON><PERSON>',
    'Fechar aviso',
  ];

  bool encontrou;
  do {
    encontrou = false;
    for (final texto in botoesPopup) {
      final Finder botao = find.text(texto);
      if (await tester.pumpAndSettle() == 0) break;
      if (botao.evaluate().isNotEmpty) {
        await tester.tap(botao);
        await tester.pumpAndSettle();
        encontrou = true;
      }
    }
  } while (encontrou);
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('Gerar um demonstrativo de IR', (WidgetTester tester) async {
    await app.main();
    await tester.pumpAndSettle();

    await loginCPF(tester, '88229513368', '123456');
    await tester.pumpAndSettle();

    // Fecha popups iniciais, se houver
    await fecharPopups(tester);

    final Finder buttonFinanceiro = find.text('Financeiro');
    await tester.tap(buttonFinanceiro);
    await tester.pumpAndSettle();

    await fecharPopups(tester);

    final Finder buttonDemonstrativoIR = find.text('Demonstrativo de imposto de renda');
    await tester.tap(buttonDemonstrativoIR);
    await tester.pumpAndSettle();

    await fecharPopups(tester);

    final Finder buttonGerarRelatorio = find.text('Gerar Relatório');
    await tester.tap(buttonGerarRelatorio);
    await tester.pumpAndSettle();

    await fecharPopups(tester);

    final Finder buttonFechar = find.text('Fechar');
    if (buttonFechar.evaluate().isNotEmpty) {
      await tester.tap(buttonFechar);
      await tester.pumpAndSettle();
    }
  });
}
