import 'dart:io';

import 'package:cliente_minha_unimed/models/document-required.dart';
import 'package:cliente_minha_unimed/models/electronic-guide.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class AuthorizationDocumentsEvent extends Equatable {}

class AttachFile extends AuthorizationDocumentsEvent {
  final String card;
  final File file;
  final File? compressedFile;
  final int index;
  final String? type;
  final String id;

  AttachFile(
      {required this.card,
      required this.file,
      this.compressedFile,
      this.type,
      required this.id,
      required this.index});

  @override
  List<Object> get props => [card, file];
}

class AttachFiles extends AuthorizationDocumentsEvent {
  final List<DocumentRequired> files;

  AttachFiles({required this.files});

  @override
  List<Object> get props => [files];
}

class RemoveAttachedFile extends AuthorizationDocumentsEvent {
  final int index;
  final String? chave;

  @override
  List<Object> get props => [index];

  RemoveAttachedFile({required this.index, required this.chave});
}

class EditAttachedFile extends AuthorizationDocumentsEvent {
  final int index;
  final String? chave;
  final File file;
  final File newFile;
  final String? type;
  final String id;

  @override
  List<Object> get props => [file, newFile];

  EditAttachedFile({
    required this.index,
    required this.chave,
    required this.file,
    required this.newFile,
    required this.type,
    required this.id,
  });
}

class StopAttachedFile extends AuthorizationDocumentsEvent {
  @override
  List<Object> get props => [];
}

class LoadAttachedFiles extends AuthorizationDocumentsEvent {
  final List<String> attachment;
  final String cdAgeCir;
  final String card;

  @override
  List<Object> get props => [attachment, cdAgeCir, card];

  LoadAttachedFiles(
      {required this.attachment, required this.cdAgeCir, required this.card});
}

class RemoveAllAttachedFiles extends AuthorizationDocumentsEvent {
  @override
  List<Object> get props => [];
}

class SendAttachedFiles extends AuthorizationDocumentsEvent {
  final ListaServicos servico;
  final String protocolo;
  final Perfil perfil;

  @override
  List<Object> get props => [servico, perfil];

  SendAttachedFiles(
      {required this.servico, required this.protocolo, required this.perfil});
}
