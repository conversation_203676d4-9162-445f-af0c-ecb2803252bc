import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/verificar-pendencia/verificar_pendencia_event.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/verificar-pendencia/verificar_pendencia_state.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

class VerificarPendenciaBloc
    extends Bloc<VerificarPendenciaEvent, VerificarPendenciaState> {
  VerificarPendenciaBloc() : super(InitialVerificarPendenciaState());

  @override
  Stream<VerificarPendenciaState> mapEventToState(
    VerificarPendenciaEvent event,
  ) async* {
    if (event is GetStatusProtocolo) {
      yield LoadingVerificarPendenciaState();

      try {
        final _retorno = await Locator.instance
            .get<AgendamentoApi>()
            .getStatusProtocolo(protocolo: event.agendamentoVO.protocolo);
        yield DoneVerificarPendenciaState(retornoProtocolo: _retorno);
      } catch (err) {
        yield ErrorVerificarPendenciaState('$err');
      }
    }
  }
}
