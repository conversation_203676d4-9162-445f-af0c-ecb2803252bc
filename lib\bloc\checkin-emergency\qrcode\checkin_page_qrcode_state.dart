part of 'checkin_page_qrcode_bloc.dart';

abstract class CheckinQrCodeState extends Equatable {
  const CheckinQrCodeState();

  @override
  List<Object?> get props => [];
}

class CheckinQrCodeInitial extends CheckinQrCodeState {}

class LoadingCheckinQrCodeState extends CheckinQrCodeState {}

class ErrorCheckinQrCodeState extends CheckinQrCodeState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorCheckinQrCodeState(this.message);
}

class LoadedCQrCodeState extends CheckinQrCodeState {
  final String? qrCode;

  @override
  List<Object?> get props => [qrCode];

  LoadedCQrCodeState({this.qrCode});
}

class SucessLoadedCheckinState extends CheckinQrCodeState {
  final CheckinDataModel? checkinData;

  @override
  List<Object?> get props => [checkinData];

  SucessLoadedCheckinState({this.checkinData});
}
