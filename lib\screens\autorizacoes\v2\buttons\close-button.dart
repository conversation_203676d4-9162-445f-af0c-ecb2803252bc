import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/shared/i18n/i18n_helper.dart';
import 'package:flutter/material.dart';

class CloseDetailButton extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: UnimedColors.greenDark,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: () {
              Navigator.pop(context);
            },
            child: Padding(
              padding: const EdgeInsets.only(
                  top: 8.0, bottom: 8.0, left: 24.0, right: 24.0),
              child: Text(I18nHelper.translate(context, 'common.close'),
                  style: TextStyle(
                    color: UnimedColors.white,
                  )),
            ),
          ),
        ),
      ],
    );
  }
}
