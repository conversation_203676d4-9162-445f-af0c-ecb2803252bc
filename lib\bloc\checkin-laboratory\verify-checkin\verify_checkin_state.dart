part of 'verify_checkin_bloc.dart';

abstract class VerifyCheckinState extends Equatable {
  const VerifyCheckinState();

  @override
  List<Object> get props => [];
}

class VerifyCheckinInitial extends VerifyCheckinState {}

class QrcodeExpiredState extends VerifyCheckinState {}

class ExistingCheckinState extends VerifyCheckinState {
  final String id;
  final String expiredAt;

  ExistingCheckinState({required this.id, required this.expiredAt});
}

class ErrorVerifyCheckinState extends VerifyCheckinState {
  final String message;

  ErrorVerifyCheckinState({required this.message});
}

class DontExistCheckinState extends VerifyCheckinState {}

class LoadingVerifyCheckinState extends VerifyCheckinState {}

class LoadingSendCheckinState extends VerifyCheckinState {}

class SuccessSendCheckinState extends VerifyCheckinState {
  final String qrCode;

  @override
  List<Object> get props => [qrCode];

  SuccessSendCheckinState({required this.qrCode});
}

class ErrorSendCheckinState extends VerifyCheckinState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorSendCheckinState({required this.message});
}


