import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

import '../../models/agenda-agendamento.model.dart';

@immutable
abstract class ConsultaEvent extends Equatable {}

class ListAllPrestadoresClinicaUnimedEvent extends ConsultaEvent {
  final ProviderModel clinicaUnimed;
  final EspecialtyModel especialidade;
  final Perfil? perfil;
  final bool teleconsulta;

  @override
  List<Object?> get props =>
      [clinicaUnimed, especialidade, perfil, teleconsulta];

  ListAllPrestadoresClinicaUnimedEvent({
    required this.clinicaUnimed,
    required this.especialidade,
    required this.perfil,
    required this.teleconsulta,
  });
}

class ListAllAgendasPrestadorEvent extends ConsultaEvent {
  final ProviderModel? prestador;
  final EspecialtyModel? especialidade;
  final AddressModel? endereco;
  final Perfil? perfil;
  final bool isTeleconsulta;
  final ProviderModel? clinicaUnimed;
  final DateTime dataInicio;
  final DateTime? dataFim;

  @override
  List<Object?> get props => [
        prestador,
        especialidade,
        endereco,
        perfil,
        isTeleconsulta,
        clinicaUnimed,
        dataInicio,
        dataFim,
      ];

  ListAllAgendasPrestadorEvent({
    required this.prestador,
    required this.especialidade,
    required this.endereco,
    required this.perfil,
    required this.isTeleconsulta,
    required this.dataInicio,
    required this.dataFim,
    this.clinicaUnimed,
  });
}

class ResetConsultaEvent extends ConsultaEvent {
  ResetConsultaEvent();

  @override
  List<Object> get props => [];
}

class AdvanceStep extends ConsultaEvent {
  final int indexPage;

  @override
  List<Object?> get props => [indexPage];

  AdvanceStep(this.indexPage);
}

class SelectEspecialty extends ConsultaEvent {
  final EspecialtyModel especialtyModel;

  @override
  List<Object?> get props => [especialtyModel];

  SelectEspecialty(this.especialtyModel);
}

class SelectAddress extends ConsultaEvent {
  final AddressModel addressModel;

  @override
  List<Object?> get props => [addressModel];

  SelectAddress(this.addressModel);
}

class SelectHorario extends ConsultaEvent {
  final AgendaAgendamentoVO horario;

  @override
  List<Object?> get props => [horario];

  SelectHorario(this.horario);
}

class BackStep extends ConsultaEvent {
  final int indexPage;

  @override
  List<Object?> get props => [indexPage];

  BackStep(this.indexPage);
}
