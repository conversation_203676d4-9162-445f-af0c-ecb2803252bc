import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation__event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/provider_solicitation_state.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider-solicitation.model.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/guia-medico.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';

class ProviderSolicitationBloc
    extends Bloc<ProviderSolicitationEvent, ProviderSolicitationState> {
  ProviderSolicitationBloc() : super(ProviderSolicitationInitialState());
  final logger = UnimedLogger(className: 'ProviderSolicitationBloc');

  Iterable<ProviderSolicitationModel> _prestadores = [];
  Iterable<ProviderSolicitationModel> get prestadores => _prestadores;

  ProviderSolicitationModel? _providerSolicitation;
  ProviderSolicitationModel? get providerSolicitation => _providerSolicitation;

  @override
  Stream<ProviderSolicitationState> mapEventToState(
    ProviderSolicitationEvent event,
  ) async* {
    if (event is ListAllProviderSolicitationEvent) {
      yield ProviderSolicitationLoadingState();
      final api = Locator.instance.get<GuiaMedicoApi>();

      try {
        logger.d('ListAll INIT');

        _prestadores = await api.listProvidersSolicitation(event.perfil!);

        logger.d('ListAll END');
        yield ProviderSolicitationDoneState(_prestadores);
      } catch (e) {
        logger.e('ProviderSolicitationBloc ListAll error $e');
        if (e is SocketException || e is HandshakeException) {
          yield ProviderSolicitationErrorState(
            "Falha ao tentar carregar os Hospitais, tente novamente mais tarde.",
          );
        } else {
          yield ProviderSolicitationErrorState('$e');
        }
      }
    } else if (event is SelectProviderSolicitationEvent) {
      debugPrint("======== ${event.providerSolicitation?.providerName}");
      _providerSolicitation = event.providerSolicitation;

      yield DoneSelectProviderSolicitationState(_providerSolicitation);
    }
  }
}
