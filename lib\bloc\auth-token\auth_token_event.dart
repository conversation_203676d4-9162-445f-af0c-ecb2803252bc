part of 'auth_token_bloc.dart';

abstract class AuthTokenEvent extends Equatable {
  const AuthTokenEvent();

  @override
  List<Object?> get props => [];
}

class RequestGeolocationPermissionEvent extends AuthTokenEvent {
  RequestGeolocationPermissionEvent();

  @override
  List<Object?> get props => [];
}

class OpenAppConfigsEvent extends AuthTokenEvent {
  OpenAppConfigsEvent();

  @override
  List<Object?> get props => [];
}

class GetAuthToken extends AuthTokenEvent {
  final Carteira? beneficiaryCard;
  final bool forceUpdate;
  final bool requestLocation;
  final bool requestPermission;
  GetAuthToken({
    required this.beneficiaryCard,
    this.forceUpdate = false,
    this.requestLocation = false,
    this.requestPermission = false,
  });
  @override
  List<Object?> get props => [beneficiaryCard];
}

class RegisterDevice extends AuthTokenEvent {
  final String userId;
  final String so;
  final Map<String, dynamic> deviceData;
  RegisterDevice({
    required this.userId,
    required this.so,
    required this.deviceData,
  });
  @override
  List<Object?> get props => [];
}
