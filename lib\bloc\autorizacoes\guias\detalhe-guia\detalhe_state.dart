part of 'detalhe_bloc.dart';

abstract class DetalheState extends Equatable {
  const DetalheState();

  @override
  List<Object> get props => [];
}

class DetalheInitial extends DetalheState {}

class ErrorCarregarDetalheState extends DetalheState {
  final String message;

  ErrorCarregarDetalheState({required this.message});
}

class SuccessCarregarDetalheState extends DetalheState {
  final DetalheGuiaModel guia;

  SuccessCarregarDetalheState({required this.guia});
}

class LoadingCarregarDetalheState extends DetalheState {}
