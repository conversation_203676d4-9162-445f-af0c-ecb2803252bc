part of 'detalhe_guia_bloc.dart';

abstract class DetalheGuiaState extends Equatable {
  const DetalheGuiaState();

  @override
  List<Object> get props => [];
}

class DetalheGuiaInitial extends DetalheGuiaState {}

class ErrorBuscarDetalheState extends DetalheGuiaState {
  final String message;

  ErrorBuscarDetalheState({required this.message});
}

class LoadingBuscarDetalheState extends DetalheGuiaState {}

class SuccessBuscarDetalheState extends DetalheGuiaState {
  final List<DetalheTimeline> timeline;

  SuccessBuscarDetalheState({required this.timeline});
}
