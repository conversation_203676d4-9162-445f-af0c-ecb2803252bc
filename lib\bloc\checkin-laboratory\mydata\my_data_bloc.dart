import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-lab/checkinLab.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-lab/checkin-lab.api.dart';
import 'package:cliente_minha_unimed/shared/constants.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
part 'my_data_event.dart';
part 'my_data_state.dart';

class CheckinLaboratoryMyDataBloc
    extends Bloc<CheckinLaboratoryMyDataEvent, CheckinLaboratoryMyDataState> {
  CheckinLaboratoryMyDataBloc() : super(CheckinLaboratoryMyDataInitialState());

  @override
  Stream<CheckinLaboratoryMyDataState> mapEventToState(
    CheckinLaboratoryMyDataEvent event,
  ) async* {
    if (event is GetCurrentCheckinLab) {
      yield LoadingCheckinLaboratoryMyDataState();
      try {
        CheckinLabModel? _checkinLabData =
            await Locator.instance.get<CheckinLabApi>().getCurrentCheckinLab(
                  event.perfil!.contratoBeneficiario.carteira!.carteiraNumero,
                  event.user,
                );

        if (_checkinLabData != null) {
          yield SucessLoadedCheckinLabState(checkinLabModel: _checkinLabData);
        } else {
          final _contactsModel = await Locator.instance
              .get<BeneficiarioApi>()
              .getAllContacts(
                  codBenef: event
                      .perfil!.contratoBeneficiario.beneficiario!.codBenef);

          yield LoadedCheckinLaboratoryMyDataState(
              email:
                  _contactsModel.getPreferencial(CONTACT_TYPE_EMAIL)?.contato ??
                      "",
              telefone:
                  _contactsModel.getPreferencial(CONTACT_TYPE_CELL)?.contato ??
                      "");
        }
      } catch (ex) {
        yield ErrorCheckinLabState(message: ex.toString());
      }
    }
  }
}
