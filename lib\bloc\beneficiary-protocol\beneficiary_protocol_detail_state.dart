part of 'beneficiary_protocol_detail_bloc.dart';

abstract class BeneficiaryProtocolDetailState extends Equatable {
  const BeneficiaryProtocolDetailState();

  @override
  List<Object> get props => [];
}

class BeneficiaryProtocolDetailInitial extends BeneficiaryProtocolDetailState {}

class ErrorBeneficiaryProtocolDetailState
    extends BeneficiaryProtocolDetailState {
  final String message;

  ErrorBeneficiaryProtocolDetailState({required this.message});
}

class LoadedBeneficiaryProtocolDetailState
    extends BeneficiaryProtocolDetailState {
  final BeneficiaryProtocolDetailModel detail;

  LoadedBeneficiaryProtocolDetailState({
    required this.detail,
  });
}

class LoadingBeneficiaryProtocolDetailState
    extends BeneficiaryProtocolDetailState {}

class NoDataBeneficiaryProtocolDetailState
    extends BeneficiaryProtocolDetailState {}
