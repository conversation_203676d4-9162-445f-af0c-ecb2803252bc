import 'package:cliente_minha_unimed/models/medical-guide/provider-solicitation.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class ProviderSolicitationEvent extends Equatable {}

class ListAllProviderSolicitationEvent extends ProviderSolicitationEvent {
  final Perfil? perfil;

  @override
  List<Object?> get props => [perfil];

  ListAllProviderSolicitationEvent({required this.perfil});
}

class SelectProviderSolicitationEvent extends ProviderSolicitationEvent {
  final ProviderSolicitationModel? providerSolicitation;

  @override
  List<Object?> get props => [providerSolicitation];

  SelectProviderSolicitationEvent({required this.providerSolicitation});
}
