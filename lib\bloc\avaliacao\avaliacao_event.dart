import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class EvaluationEvent extends Equatable {}

class CheckEvaluationEvent extends EvaluationEvent {
  final int? minutes;
  final bool? enable;
  @override
  List<Object> get props => [];

  CheckEvaluationEvent({required this.enable, this.minutes});
}

class SetSuccessEvaluationEvent extends EvaluationEvent {
  final bool? success;

  @override
  List<Object> get props => [];

  SetSuccessEvaluationEvent({this.success});
}

class SendEvaluationEvent extends EvaluationEvent {
  final double? stars;
  final String? comment;
  final String? servico;
  final bool? sendPerfilApps;
  final String typePa;
  final int atendimento;

  @override
  List<Object> get props => [];

  SendEvaluationEvent({
    this.stars,
    this.comment,
    this.servico,
    this.sendPerfilApps,
    this.atendimento = 0,
    this.typePa = "",
  });
}
