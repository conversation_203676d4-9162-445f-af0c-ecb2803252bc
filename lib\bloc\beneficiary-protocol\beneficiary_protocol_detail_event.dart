part of 'beneficiary_protocol_detail_bloc.dart';

abstract class BeneficiaryProtocolDetailEvent extends Equatable {
  const BeneficiaryProtocolDetailEvent();

  @override
  List<Object> get props => [];
}

class GetBeneficiaryProtocolDetail extends BeneficiaryProtocolDetailEvent {
  final BeneficiaryProtocolModel beneficiaryProtocol;

  GetBeneficiaryProtocolDetail({
    required this.beneficiaryProtocol,
  });
}

class FilterBeneficiaryProtocolDetail extends BeneficiaryProtocolDetailEvent {
  final List<String> tags;
  final List<String> status;

  @override
  List<Object> get props => [tags, status];

  FilterBeneficiaryProtocolDetail({required this.tags, required this.status});
}
