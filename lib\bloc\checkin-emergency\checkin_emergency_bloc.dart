import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-emergency/checkin-data.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/virtual-emergency-service/virtual-emergency-service.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
part 'checkin_emergency_event.dart';
part 'checkin_emergency_state.dart';

class CheckinEmergencyBloc extends Bloc<CheckinEmergencyEvent, int> {
  CheckinEmergencyBloc() : super(0);
  final int maxPages = 5;

  int _pageIndex = 0;
  int get pageIndex => _pageIndex;
  int _qtdPa = 0;
  int get qtdPa => _qtdPa;
  bool _showRedictAlert = false;
  bool get showRedictAlert => _showRedictAlert;
  updatePageIndex(pageIndex) => _pageIndex = pageIndex;

  CheckinDataModel? _checkinDataModel;
  CheckinDataModel? get checkinDataModel => _checkinDataModel;
  updateCheckinDataModel(CheckinDataModel? checkinDataModel) =>
      _checkinDataModel = checkinDataModel;

  @override
  Stream<int> mapEventToState(
    CheckinEmergencyEvent event,
  ) async* {
    if (event is NextPage) {
      _pageIndex = _pageIndex < maxPages ? _pageIndex + 1 : _pageIndex;
      if (_pageIndex == 4) {
        verifyToPA();
      }
    } else if (event is BackPage) {
      _pageIndex = _pageIndex > 0 ? _pageIndex - 1 : _pageIndex;
    } else if (event is GoToPage) {
      if (event.index < _pageIndex && _pageIndex < maxPages)
        _pageIndex = event.index;
    } else if (event is GoToLastPage) {
      _pageIndex = maxPages;
    } else if (event is CreateCheckin) {
      _checkinDataModel = event.checkinDataModel;
      _checkinDataModel!.creationDate = DateTime.now().toString();

      await Locator.instance
          .get<VirtualEmergencyServiceApi>()
          .saveCheckinToSharedPref(
            event.checkinDataModel,
            event.perfil!.contratoBeneficiario.carteira!.carteiraFormatada,
          );

      _pageIndex = _pageIndex < maxPages ? _pageIndex + 1 : _pageIndex;
    } else if (event is NewCheckin) {
      final card =
          event.perfil!.contratoBeneficiario.carteira!.carteiraFormatada;
      await Locator.instance
          .get<VirtualEmergencyServiceApi>()
          .saveCheckinToSharedPref(null, card);

      _pageIndex = 0;
    } else if (event is SetToInitialState) {
      _checkinDataModel = null;
      _pageIndex = 0;
    }

    yield _pageIndex;
  }

  verifyToPA() {
    debugPrint("ESTOU VERIFICANDO");
    _qtdPa = 0;

    _showRedictAlert = _checkinDataModel!.canRedirectToPa;
    _checkinDataModel!.checklistPain!.forEach((element) {
      if (element.redirectPa! && element.selected!) {
        _qtdPa++;
      }
    });
    _checkinDataModel!.checklistSymtons!.forEach((element) {
      if (element.redirectPa! && element.selected!) {
        _qtdPa++;
      }
    });
  }
}
