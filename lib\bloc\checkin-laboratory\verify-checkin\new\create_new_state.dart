part of 'create_new_bloc.dart';

abstract class CreateNewState extends Equatable {
  const CreateNewState();
  
  @override
  List<Object> get props => [];
}

class CreateNewInitial extends CreateNewState {}

class LoadingDeleteQrCodeState extends CreateNewState {}

class ErrorDeleteQrCodeState extends CreateNewState {
  final String message;
  @override
  List<Object> get props => [message];

  ErrorDeleteQrCodeState({required this.message});
}

class SucessDeleteQrCodeState extends CreateNewState {}
