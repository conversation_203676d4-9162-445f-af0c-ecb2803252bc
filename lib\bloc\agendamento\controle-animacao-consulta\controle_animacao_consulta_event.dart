import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

@immutable
abstract class ControleAnimacaoConsultaEvent extends Equatable {}

class MudarParaEspecialidade extends ControleAnimacaoConsultaEvent {
  final ProviderModel? clinicaUnimed;
  final Perfil? perfil;
  MudarParaEspecialidade({this.clinicaUnimed, this.perfil});

  @override
  List<Object?> get props => [clinicaUnimed, perfil];
}

class MudarParaAgenda extends ControleAnimacaoConsultaEvent {
  final ProviderModel? prestador;
  final ProviderModel? clinicaUnimed;
  final EspecialtyModel? especialidade;
  MudarParaAgenda({this.prestador, this.clinicaUnimed, this.especialidade});
  @override
  List<Object?> get props => [prestador, clinicaUnimed, especialidade];
}
