import 'package:cliente_minha_unimed/models/password_rules.model.dart';
import 'package:cliente_minha_unimed/shared/api/cadastro.api.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class CadastroEnvioState extends Equatable {}

class InitialState extends CadastroEnvioState {
  @override
  List<Object> get props => [];
}

class LoadingState extends CadastroEnvioState {
  @override
  List<Object> get props => [];
}

class LoadingRullesPasswordState extends CadastroEnvioState {
  @override
  List<Object> get props => [];
}

class ErrorState extends CadastroEnvioState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorState(this.message);
}

class ErrorRulesState extends CadastroEnvioState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorRulesState(this.message);
}

class DoneState extends CadastroEnvioState {
  final RetornoEnvioCadastro retorno;

  @override
  List<Object> get props => [retorno];

  DoneState(this.retorno);
}

class DoneGetPasswordRulesState extends CadastroEnvioState {
  final List<PasswordRulesModel> rules;

  @override
  List<Object?> get props => [];

  DoneGetPasswordRulesState({required this.rules});
}
