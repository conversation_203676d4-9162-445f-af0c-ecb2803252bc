import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class GuideSolicitationState extends Equatable {}

class InitialGuideState extends GuideSolicitationState {
  @override
  List<Object> get props => [];
}

class LoadingGuideSolicitationState extends GuideSolicitationState {
  @override
  List<Object> get props => [];
}

class ErrorGuideSolicitationState extends GuideSolicitationState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorGuideSolicitationState(this.message);
}

class DoneGetGuidesPdfState extends GuideSolicitationState {
  final List<String> guidesPath;

  @override
  List<Object> get props => [];

  DoneGetGuidesPdfState({required this.guidesPath});
}

class EmptyGuidesPdfState extends GuideSolicitationState {
  @override
  List<Object> get props => [];
}
