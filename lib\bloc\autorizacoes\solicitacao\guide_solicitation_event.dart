import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class GuideSolicitationEvent extends Equatable {}

class GetGuidesPdf extends GuideSolicitationEvent {
  final String numProtocolo;
  final int numAtend;

  @override
  List<Object> get props => [];

  GetGuidesPdf({
    required this.numProtocolo,
    required this.numAtend,
  });
}

class GetGuidesPdfV2 extends GuideSolicitationEvent {
  final String numProtocolo;
  final int? numAtend;

  @override
  List<Object> get props => [];

  GetGuidesPdfV2({
    required this.numProtocolo,
    required this.numAtend,
  });
}
