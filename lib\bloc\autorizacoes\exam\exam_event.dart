import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';

abstract class ExamEvent extends Equatable {
  const ExamEvent();

  @override
  List<Object?> get props => [];
}

class GetListExamsEvent extends ExamEvent {
  final Perfil? perfil;
  @override
  List<Object?> get props => [perfil];
  GetListExamsEvent({this.perfil});
}

class GetDetailsEvent extends ExamEvent {
  final Perfil perfil;
  final int codSolicitacao;
  @override
  List<Object?> get props => [perfil, codSolicitacao];
  GetDetailsEvent({
    required this.perfil,
    required this.codSolicitacao,
  });
}

class GeneratePdf extends ExamEvent {
  final Perfil? perfil;
  final int? cod;
  @override
  List<Object?> get props => [perfil, cod];
  GeneratePdf({this.perfil, this.cod});
}
