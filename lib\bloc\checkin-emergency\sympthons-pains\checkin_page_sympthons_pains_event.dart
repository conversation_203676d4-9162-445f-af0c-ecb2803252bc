part of 'checkin_sympthons_pains_bloc.dart';

abstract class CheckinPageSympthonsPainsEvent extends Equatable {
  const CheckinPageSympthonsPainsEvent();

  @override
  List<Object> get props => [];
}

class CheckinDoneSympthons extends CheckinPageSympthonsPainsEvent {}

class CheckinBackToSympthons extends CheckinPageSympthonsPainsEvent {}

class ResetCheckinAttendance extends CheckinPageSympthonsPainsEvent {}
