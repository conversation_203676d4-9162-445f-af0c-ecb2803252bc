import 'dart:async';

import 'package:cliente_minha_unimed/models/beneficiary-protocol-detail.model.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/graphql.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:graphql/client.dart';
import 'package:http_client/http_client.dart';
import 'package:intl/intl.dart';

class BeneficiaryProtocolApi extends GraphQlApi {
  BeneficiaryProtocolApi(UnimedHttpClient httpClient) : super(httpClient);

  final logger = UnimedLogger(className: 'BeneficiaryProtocolApi');

  Future<List<BeneficiaryProtocolModel>> getBeneficiaryProtocols(
      {required Perfil profile, DateTimeRange? dateTimeRange}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String date = "";
      if (dateTimeRange != null) {
        date = '''
          startDate: "${DateFormat("yyyy-MM-dd").format(dateTimeRange.start)}"
          endDate:  "${DateFormat("yyyy-MM-dd").format(dateTimeRange.end)}"
        ''';
      }

      String query = '''
        query BeneficiaryProtocols {
            beneficiaryProtocols(codUnimed: ${profile.carteira?.unimedCarteira},
             codCard:  ${profile.carteira?.codCarteira}, 
             dvCard: "${profile.carteira?.digitoCarteira}",
             $date 
            ) {
              requesterName
              status
              protocolType
              ansProtocolNumberHistory {
                  ansProtocolNumber
                  protocolDate
                  transactionNumber
                  ansProtocolOrigin {
                      originCode
                  }
                  atendAuditPrevent {
                      numAtend
                  }
              }
            }
        }
      ''';

      logger.e('getBeneficiaryProtocols query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 120));

      if (result.hasException) {
        logger.e('getBeneficiaryProtocols exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.first.message ??
            MessageException.GENERAL;

        throw UnimedException(message);
      } else {
        final List<dynamic> data = result.data!['beneficiaryProtocols'] as List;
        logger.d('getBeneficiaryProtocols success list ${data.length}');

        final List<BeneficiaryProtocolModel> collection = data.map((e) {
          return BeneficiaryProtocolModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (e) {
      logger.e('getBeneficiaryProtocols UnimedException : ${e.message}');
      throw UnimedException(e.message);
    } on TimeoutException catch (e) {
      logger.e('getBeneficiaryProtocols TimeoutException : $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } catch (ex) {
      logger.e('getBeneficiaryProtocols exception : $ex');
      throw UnimedException(MessageException.GENERAL);
    }
  }

  Future<BeneficiaryProtocolDetailModel> getBeneficiaryProtocolDetails(
      {required BeneficiaryProtocolModel beneficiaryProtocol}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      debugPrint("beneficiaryProtocol: ${beneficiaryProtocol.toJson()}");

      String transactionNumber = beneficiaryProtocol
                  .ansProtocolNumberHistory.transactionNumber !=
              null
          ? "transactionNumber: ${beneficiaryProtocol.ansProtocolNumberHistory.transactionNumber}"
          : "";
      String numAtend = beneficiaryProtocol
                  .ansProtocolNumberHistory.atendAuditPrevent?.numAtend !=
              null
          ? "numAtend: ${beneficiaryProtocol.ansProtocolNumberHistory.atendAuditPrevent?.numAtend}"
          : "";
      String originCode = beneficiaryProtocol
                  .ansProtocolNumberHistory.ansProtocolOrigin?.originCode !=
              null
          ? '''originCode: "${beneficiaryProtocol.ansProtocolNumberHistory.ansProtocolOrigin!.originCode!}"'''
          : "";

      String query = '''
        query AnalysisDetailsByProtocol {
            analysisDetailsByProtocol(
                ansProtocolNumber: "${beneficiaryProtocol.ansProtocolNumberHistory.ansProtocolNumber}"
                $transactionNumber          
                $originCode
                $numAtend
            ) {
                details
                deadline
                appointments {
                  appointmentDateTime
                  specialty
                  location
                  doctor
                }
            }
        }
      ''';

      logger.e('getBeneficiaryProtocolDetail query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 120));

      if (result.hasException) {
        logger
            .e('getBeneficiaryProtocolDetail exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.first.message ??
            MessageException.GENERAL;

        throw UnimedException(message);
      } else {
        final Map<String, dynamic> data =
            result.data!['analysisDetailsByProtocol'];
        logger.d('getBeneficiaryProtocolDetail success ${data}');

        return BeneficiaryProtocolDetailModel.fromJson(data);
      }
    } on UnimedException catch (e) {
      logger.e('getBeneficiaryProtocols UnimedException : ${e.message}');
      throw UnimedException(e.message);
    } on TimeoutException catch (e) {
      logger.e('getBeneficiaryProtocols TimeoutException : $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } catch (ex) {
      logger.e('getBeneficiaryProtocols exception : $ex');
      throw UnimedException(MessageException.GENERAL);
    }
  }

  Future<List<String>> getGuidePdfV2(
      {required String protocol, int? numAtend}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String numAtendQuery = numAtend != null ? 'numAtend: "$numAtend"' : '';

      String query = '''
        query AuthorizationGetGuidesBase64 {
            authorizationGetGuidesBase64(protocol: "${protocol}"
            $numAtendQuery
            )
        }
      ''';

      logger.e('getGuidePdfV2 query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 120));

      if (result.hasException) {
        logger.e('getGuidePdfV2 exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.first.message ??
            MessageException.GENERAL;

        throw UnimedException(message);
      } else {
        final List<dynamic> data =
            result.data!['authorizationGetGuidesBase64'] as List;
        logger.d('getGuidePdfV2 success list ${data.length}');

        return data.map((e) {
          return e.toString();
        }).toList();
      }
    } on UnimedException catch (e) {
      logger.e('getBeneficiaryProtocols UnimedException : ${e.message}');
      throw UnimedException(e.message);
    } on TimeoutException catch (e) {
      logger.e('getBeneficiaryProtocols TimeoutException : $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } catch (ex) {
      logger.e('getBeneficiaryProtocols exception : $ex');
      throw UnimedException(MessageException.GENERAL);
    }
  }
}
