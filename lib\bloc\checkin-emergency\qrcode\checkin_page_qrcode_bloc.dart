import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-emergency/checkin-data.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/screens/home/<USER>';
import 'package:cliente_minha_unimed/shared/api/beneficiario.api.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-emergency/checkin_emercency.api.dart';
import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';

part 'checkin_page_qrcode_event.dart';
part 'checkin_page_qrcode_state.dart';

class CheckinQrCodeBloc extends Bloc<CheckinQrCodeEvent, CheckinQrCodeState> {
  CheckinQrCodeBloc() : super(CheckinQrCodeInitial());

  PA? _paConfig;
  PA? get pa => _paConfig;

  @override
  Stream<CheckinQrCodeState> mapEventToState(
    CheckinQrCodeEvent event,
  ) async* {
    if (event is GetQRCodeDataEvent) {
      yield LoadingCheckinQrCodeState();
      try {
        if (event.checkinDataModel!.requestEmailTerm != null &&
            event.checkinDataModel!.requestEmailTerm == true) {
          try {
            Locator.instance.get<BeneficiarioApi>().sendEmailTerms(
                  event.checkinDataModel!.profileEmail,
                  event.user!.config.documents!.useTermsCheckinEmergency,
                  event.perfil,
                );
          } catch (ex) {
            logger.e('GetQRCodeDataEvent -  Erro on sendEmaelTerms -  $ex');
          }
        }

        final qrCodeData = Locator.instance
            .get<CheckinEmergencyApi>()
            .getQRCodeCheckinEmergency(
              user: event.user!,
              perfil: event.perfil!,
              checkinDataModel: event.checkinDataModel!,
              paconfig: event.paConfig,
            );

        yield LoadedCQrCodeState(qrCode: qrCodeData);
      } catch (ex) {
        yield ErrorCheckinQrCodeState(ex.toString());
      }
    }
  }
}
