import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/remote-config/banner-remote-config.model.dart';

@immutable
abstract class BannersState extends Equatable {}

class InitialState extends BannersState {
  @override
  List<Object> get props => [];
}

class LoadingState extends BannersState {
  @override
  List<Object> get props => [];
}

class ErrorState extends BannersState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorState(this.message);
}

class DoneState extends BannersState {
  final List<BannerRemoteConfig> banners;

  @override
  List<Object> get props => [banners];

  DoneState(this.banners);
}

class NoBannerDataState extends BannersState {
  @override
  List<Object> get props => [];

  NoBannerDataState();
}
