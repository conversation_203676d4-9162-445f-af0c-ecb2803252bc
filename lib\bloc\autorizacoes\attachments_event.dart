import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/file-attach.model.dart';

@immutable
abstract class AutorizacaoAttachmentsEvent extends Equatable {}

class AttachFile extends AutorizacaoAttachmentsEvent {
  final File file;
  final File? compressedFile;

  AttachFile({required this.file, this.compressedFile});

  @override
  List<Object> get props => [file];
}

class AttachFiles extends AutorizacaoAttachmentsEvent {
  final List<FileAttach>? files;

  AttachFiles({required this.files});

  @override
  List<Object?> get props => [files];
}

class RemoveAttachedFile extends AutorizacaoAttachmentsEvent {
  final FileAttach file;

  @override
  List<Object> get props => [file];

  RemoveAttachedFile({required this.file});
}

class EditAttachedFile extends AutorizacaoAttachmentsEvent {
  final File file;
  final File newFile;

  @override
  List<Object> get props => [file, newFile];

  EditAttachedFile({required this.file, required this.newFile});
}

class StopAttachedFile extends AutorizacaoAttachmentsEvent {
  @override
  List<Object> get props => [];
}

class LoadAttachedFile extends AutorizacaoAttachmentsEvent {
  @override
  List<Object> get props => [];
}

class RemoveAllAttachedFiles extends AutorizacaoAttachmentsEvent {
  @override
  List<Object> get props => [];
}
