import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-lab/guide.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-lab/guide.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'guide_event.dart';
part 'guide_state.dart';

class GuideBloc extends Bloc<GuideEvent, GuideState> {
  GuideBloc() : super(GuideInitial());

  // GuideModel _guideData;
  // GuideModel get guideData => _guideData ?? GuideModel();

  @override
  Stream<GuideState> mapEventToState(
    GuideEvent event,
  ) async* {
    if (event is LoadListGuide) {
      yield LoadingListGuide();
      try {
        final response = await Locator.instance
            .get<GuideApi>()
            .getGuides(event.perfil!, event.checkinLabConfig!);
        yield SuccessListGuide(guides: response);
      } on NoDataException catch (_) {
        yield NoDataListGuide(message: "Não existem guias para sua carteira.");
      } catch (ex) {
        yield ErrorListGuide(message: '$ex');
      }
    }
  }
}
