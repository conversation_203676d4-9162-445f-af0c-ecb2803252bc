import 'dart:async';

import 'package:bloc/bloc.dart';

import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push-confirmar/confirmar_agendamento_state.dart';
import 'package:cliente_minha_unimed/bloc/agendamento/agendamento-push-confirmar/confirmar_agendamento_event.dart';
import 'package:cliente_minha_unimed/shared/api/agendamento/agendamento.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';

class ConfirmarAgendamentoBloc
    extends Bloc<ConfirmarAgendamentoEvent, ConfirmarAgendamentoState> {
  ConfirmarAgendamentoBloc() : super(ConfirmarAgendamentoInitialState());

  @override
  Stream<ConfirmarAgendamentoState> mapEventToState(
    ConfirmarAgendamentoEvent event,
  ) async* {
    if (event is ConfirmarConsultaAgendamentoEvent) {
      yield ConfirmarAgendamentoLoadingState();

      final api = Locator.instance.get<AgendamentoApi>();

      try {
     var result =   await api.confirmarConsulta(
          protocolo: event.protocolo,
          perfil: event.perfil!,
        );
        print(result);

        yield ConfirmarAgendamentoDoneState(result['mensagem']);
      } on UnimedException catch (e) {
        yield ConfirmarAgendamentoErrorState(e.message);
      } on Exception catch (e) {
        yield ConfirmarAgendamentoErrorState("$e");
      }
    }
  }
}
