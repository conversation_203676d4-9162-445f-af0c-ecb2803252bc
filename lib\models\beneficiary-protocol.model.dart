import 'package:intl/intl.dart';

class BeneficiaryProtocolModel {
  String? requesterName;
  String? status;
  late String protocolType;
  late AnsProtocolNumberHistory ansProtocolNumberHistory;

  BeneficiaryProtocolModel(
      {this.requesterName,
      this.status,
      required this.protocolType,
      required this.ansProtocolNumberHistory});

  bool get isAuthorization {
    return this.protocolType == "Autorização" ||
        this.protocolType == "AUTORIZACAO" ||
        this.protocolType == "AUTORIZAÇÃO";
  }

  bool get isFinished {
    return this.status == "Concluído" ||
        this.status == "Finalizado" ||
        this.status == "Atendido" ||
        this.status == "Encerrado";
  }

  String get dataHoraFormat {
    try {
      return DateFormat("dd/MM/yyyy - HH:mm")
          .format(DateTime.parse(this.ansProtocolNumberHistory.protocolDate));
    } catch (e) {
      return this.ansProtocolNumberHistory.protocolDate;
    }
  }

  BeneficiaryProtocolModel.fromJson(Map<String, dynamic> json) {
    requesterName = json['requesterName'];
    status = json['status'];
    protocolType = json['protocolType'];
    ansProtocolNumberHistory =
        new AnsProtocolNumberHistory.fromJson(json['ansProtocolNumberHistory']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['requesterName'] = this.requesterName;
    data['status'] = this.status;
    data['protocolType'] = this.protocolType;
    data['ansProtocolNumberHistory'] = this.ansProtocolNumberHistory.toJson();

    return data;
  }
}

class AnsProtocolNumberHistory {
  late String ansProtocolNumber;
  late String protocolDate;
  int? transactionNumber;
  AnsProtocolOrigin? ansProtocolOrigin;
  AtendAuditPrevent? atendAuditPrevent;

  AnsProtocolNumberHistory(
      {required this.ansProtocolNumber,
      required this.protocolDate,
      this.transactionNumber,
      this.ansProtocolOrigin,
      this.atendAuditPrevent});

  AnsProtocolNumberHistory.fromJson(Map<String, dynamic> json) {
    ansProtocolNumber = json['ansProtocolNumber'];
    protocolDate = json['protocolDate'];
    transactionNumber = json['transactionNumber'];
    ansProtocolOrigin = json['ansProtocolOrigin'] != null
        ? new AnsProtocolOrigin.fromJson(json['ansProtocolOrigin'])
        : null;
    atendAuditPrevent = json['atendAuditPrevent'] != null
        ? new AtendAuditPrevent.fromJson(json['atendAuditPrevent'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ansProtocolNumber'] = this.ansProtocolNumber;
    data['protocolDate'] = this.protocolDate;
    data['transactionNumber'] = this.transactionNumber;
    if (this.ansProtocolOrigin != null) {
      data['ansProtocolOrigin'] = this.ansProtocolOrigin?.toJson();
    }
    if (this.atendAuditPrevent != null) {
      data['atendAuditPrevent'] = this.atendAuditPrevent?.toJson();
    }
    return data;
  }
}

class AnsProtocolOrigin {
  String? originCode;

  AnsProtocolOrigin({this.originCode});

  AnsProtocolOrigin.fromJson(Map<String, dynamic> json) {
    originCode = json['originCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['originCode'] = this.originCode;
    return data;
  }
}

class AtendAuditPrevent {
  int? numAtend;

  AtendAuditPrevent({this.numAtend});

  AtendAuditPrevent.fromJson(Map<String, dynamic> json) {
    numAtend = json['numAtend'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['numAtend'] = this.numAtend;
    return data;
  }
}
