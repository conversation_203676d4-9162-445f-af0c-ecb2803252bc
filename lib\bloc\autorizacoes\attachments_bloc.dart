import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:path/path.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/attachments_state.dart';
import 'package:cliente_minha_unimed/models/file-attach.model.dart';

const int SIZE_FILE_BYTE = 5000000;
const String SIZE_FILE_STRING = "5Mb"; //5Mb

class AutorizacaoAttachmentsBloc
    extends Bloc<AutorizacaoAttachmentsEvent, AutorizacaoAttachmentsState> {
  AutorizacaoAttachmentsBloc() : super(InitialState());

  final logger = UnimedLogger(className: 'AutorizacaoAttachmentsBloc');

  List<FileAttach>? _attachments = List<FileAttach>.empty(growable: true);

  List<FileAttach>? get attachments => _attachments;

  int _maxFilesAttachments = 10;
  int get maxFilesAttachments => _maxFilesAttachments;

  List<String> _formatFilesAllowed = ['png', 'jpg', 'jpeg', 'pdf'];
  get formatFilesAllowed => _formatFilesAllowed;

  @override
  Stream<AutorizacaoAttachmentsState> mapEventToState(
    AutorizacaoAttachmentsEvent event,
  ) async* {
    if (event is AttachFile) {
      String name = basename(event.file.path);

      final fileSize = await event.file.length();

      if (fileSize > SIZE_FILE_BYTE) {
        yield AutorizacaoAttachmentErrorState(
          message: 'O tamanho de arquivo máximo permitido é $SIZE_FILE_STRING',
        );

        return;
      }

      if (_attachments!.length >= _maxFilesAttachments) {
        yield AutorizacaoAttachmentErrorState(
          message:
              'Você pode anexar no máximo $_maxFilesAttachments imagens por solicitação.',
        );

        return;
      } else if (_fileIsNotAllow(name)) {
        yield AutorizacaoAttachmentErrorState(
          message: 'O formato do arquivo $name não é permitido',
        );

        return;
      }

      yield LoadingState();
      final compressedFile = await FileUtils.getCompressedFile(event.file);
      _attachments!.insert(
        0,
        FileAttach(
          file: event.file,
          name: name,
          thumbnail: compressedFile,
        ),
      );

      yield DoneState(_attachments);
    } else if (event is AttachFiles) {
      yield LoadingState();

      _attachments = event.files;

      yield DoneState(_attachments);
    } else if (event is RemoveAttachedFile) {
      yield LoadingState();

      _attachments!.removeWhere((a) => a.id == event.file.id);

      yield DoneState(_attachments);
    } else if (event is EditAttachedFile) {
      yield LoadingState();

      final idx = _attachments!
          .indexWhere((fileAttach) => fileAttach.file == event.file);
      final compressedFile = await FileUtils.getCompressedFile(event.newFile);
      _attachments!.elementAt(idx).setFile(event.newFile, compressedFile);

      yield DoneState(_attachments);
    } else if (event is StopAttachedFile) {
      yield DoneState(_attachments);
    } else if (event is LoadAttachedFile) {
      yield LoadingState();
    } else if (event is RemoveAllAttachedFiles) {
      _attachments!.forEach((element) {
        FileUtils.deleteFile(element.file);
        FileUtils.deleteFile(element.thumbnail!);
      });

      _attachments = List<FileAttach>.empty(growable: true);
      yield DoneState([]);
    }
  }

  bool _fileIsNotAllow(filename) {
    try {
      final List<String> fileparts = filename.split('.');
      final String fileExtension = fileparts.last.toLowerCase();

      return !_formatFilesAllowed.contains(fileExtension);
    } catch (e) {
      logger.e(
        "AutorizacaoAttachmentsBloc Error on File Attachment validation $e",
      );

      return true;
    }
  }
}
