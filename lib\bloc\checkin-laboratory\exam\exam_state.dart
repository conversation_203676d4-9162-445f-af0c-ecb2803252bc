part of 'exam_bloc.dart';

abstract class ExamState extends Equatable {
  const ExamState();

  @override
  List<Object> get props => [];
}

class ExamInitial extends ExamState {}

class LoadingListExam extends ExamState {}

class SuccessListExam extends ExamState {
  final List<GuideExamsModel> list;
  // final List<ExamModel>? exams;
  SuccessListExam({required this.list});
}

class ErrorListExam extends ExamState {
  final String? message;
  ErrorListExam({this.message});
}
