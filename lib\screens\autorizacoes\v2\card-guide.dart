import 'package:cliente_minha_unimed/bloc/autorizacoes/eva-wpp/eva_wpp_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/beneficiary-protocol/beneficiary_protocol_detail_bloc.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol-detail.model.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol.model.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v2/buttons/atendance-button.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v2/buttons/close-button.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v2/buttons/details-buttons.dart';
import 'package:cliente_minha_unimed/screens/autorizacoes/v2/buttons/print-button.dart';
import 'package:cliente_minha_unimed/shared/base_state.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class BeneficiaryProtocolCardGuide extends StatefulWidget {
  final BeneficiaryProtocolModel beneficiaryProtocol;
  final BeneficiaryProtocolDetailModel? beneficiaryProtocolDetail;

  BeneficiaryProtocolCardGuide(
      {required this.beneficiaryProtocol, this.beneficiaryProtocolDetail});

  @override
  State<BeneficiaryProtocolCardGuide> createState() =>
      _BeneficiaryProtocolCardGuideState();
}

class _BeneficiaryProtocolCardGuideState
    extends BaseState<BeneficiaryProtocolCardGuide> {
  final _baseTranslate = 'autorizations.v2.card';

  PageController _pageController = PageController(
    initialPage: 0,
  );
  int selectedindex = 0;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: Container(
        padding: EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // //_header(widget.beneficiaryProtocol.protocolType),
              // Divider(
              //   height: 15,
              //   thickness: 1,
              // ),
              Column(
                children: [
                  _rowInfo(
                    title: translate("$_baseTranslate.requestName"),
                    value: widget.beneficiaryProtocol.requesterName ?? "",
                  ),
                  _rowInfo(
                    title: translate("$_baseTranslate.requestDate"),
                    value: widget.beneficiaryProtocol.dataHoraFormat,
                  ),
                  _rowInfo(
                    title: translate("$_baseTranslate.protocol"),
                    value: widget.beneficiaryProtocol.ansProtocolNumberHistory
                        .ansProtocolNumber,
                  ),
                  _rowInfo(
                    title: translate("$_baseTranslate.status"),
                    value: widget.beneficiaryProtocol.status ?? "",
                    color: statusColor(widget.beneficiaryProtocol.status ?? ""),
                    isBold: true,
                  ),
                ],
              ),
              if (widget.beneficiaryProtocolDetail != null) _details(),
              Divider(
                height: 15,
                thickness: 1,
              ),
              _buttons(widget.beneficiaryProtocol)
            ],
          ),
        ),
      ),
    );
  }

  Widget _details() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Divider(
          height: 15,
          thickness: 1,
        ),
        _rowInfo(
          title: translate("$_baseTranslate.details"),
          value: widget.beneficiaryProtocolDetail?.details ?? "",
        ),
        _rowInfo(
          title: translate("$_baseTranslate.deadLine"),
          value: widget.beneficiaryProtocolDetail?.deadlineFormated ?? "",
        ),
        _appointments()
      ],
    );
  }

  Widget _appointments() {
    if (widget.beneficiaryProtocolDetail?.appointments == null ||
        widget.beneficiaryProtocolDetail!.appointments!.isEmpty) {
      return Container();
    }
    final appointments = widget.beneficiaryProtocolDetail?.appointments ?? [];
    return SizedBox(
      height: 200,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(
            height: 15,
            thickness: 1,
          ),
          Expanded(
            child: PageView.builder(
              itemCount: appointments.length,
              controller: _pageController,
              onPageChanged: (int page) {
                setState(() {
                  selectedindex = page;
                });
              },
              itemBuilder: (context, index) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _rowInfo(
                      title: translate("$_baseTranslate.specialty"),
                      value: appointments[index].specialty ?? "",
                    ),
                    _rowInfo(
                      title: translate("$_baseTranslate.date"),
                      value:
                          appointments[index].appointmentDateTimeFormated ?? "",
                    ),
                    _rowInfo(
                      title: translate("$_baseTranslate.doctor"),
                      value: appointments[index].doctor ?? "",
                    ),
                    _rowInfo(
                      title: translate("$_baseTranslate.location"),
                      value: appointments[index].location ?? "",
                    ),
                  ],
                );
              },
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: _buildPageIndicator(appointments),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildPageIndicator(List<Appointment> appointments) {
    List<Widget> list = [];
    for (int i = 0; i < appointments.length; i++) {
      list.add(i == selectedindex ? _indicator(true) : _indicator(false));
    }
    return list;
  }

  Widget _indicator(bool isActive) {
    return Container(
      height: 10,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 150),
        margin: EdgeInsets.symmetric(horizontal: 4.0),
        height: isActive ? 10 : 8.0,
        width: isActive ? 12 : 8.0,
        decoration: BoxDecoration(
          boxShadow: [
            isActive
                ? BoxShadow(
                    color: Color(0XFF2FB7B2).withOpacity(0.72),
                    blurRadius: 4.0,
                    spreadRadius: 1.0,
                    offset: Offset(
                      0.0,
                      0.0,
                    ),
                  )
                : BoxShadow(
                    color: Colors.transparent,
                  )
          ],
          shape: BoxShape.circle,
          color: isActive ? UnimedColors.greenDark : Color(0XFFEAEAEA),
        ),
      ),
    );
  }

  Widget _header(String protocolType) {
    return Row(
      children: [
        Container(
          decoration: BoxDecoration(
            color: UnimedColors.greenDark2,
            shape: BoxShape.circle,
          ),
          padding: EdgeInsets.all(6),
          child: Icon(
            Icons.calendar_today_outlined,
            color: UnimedColors.greenDark,
            size: 16,
          ),
        ),
        SizedBox(width: 4),
        Container(
            decoration: BoxDecoration(
              color: protocolTypeColorBackground(protocolType),
              borderRadius: BorderRadius.circular(4),
            ),
            padding: EdgeInsets.all(2),
            child: Text(protocolType,
                style: TextStyle(
                  color: protocolTypeColor(protocolType),
                ))),
      ],
    );
  }

  Widget _rowInfo(
      {required String title,
      required String value,
      Color? color,
      bool isBold = false}) {
    return value.isEmpty
        ? Container()
        : Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "$title:",
                  style: TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Expanded(
                  child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                    builder: (context, state) {
                      return SelectableText(
                          state.isSensitiveDataVisible
                              ? StringUtils.formahideDatatMessage(value)
                              : value,
                          textAlign: TextAlign.end,
                          style: TextStyle(
                              color: color ?? UnimedColors.grayDark,
                              fontWeight: isBold
                                  ? FontWeight.bold
                                  : FontWeight.normal));
                    },
                  ),
                ),
              ],
            ),
          );
  }

  Widget _buttons(BeneficiaryProtocolModel beneficiaryProtocolModel) {
    return widget.beneficiaryProtocolDetail != null
        ? CloseDetailButton()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              BlocProvider(
                create: (context) => BeneficiaryProtocolDetailBloc(),
                child: DetailsButton(
                    beneficiaryProtocolModel: beneficiaryProtocolModel),
              ),
              BlocProvider<EvaWppBloc>(
                  create: (context) => EvaWppBloc(),
                  child: AttendanceButton(
                      beneficiaryProtocolModel: beneficiaryProtocolModel)),
              BlocProvider(
                create: (context) => GuideSolicitationBloc(),
                child: PrintButton(
                    beneficiaryProtocolModel: beneficiaryProtocolModel),
              )
            ],
          );
  }
}

Color protocolTypeColorBackground(String protocolType) {
  switch (protocolType.trim()) {
    case "Autorização":
      return UnimedColors.cream;
    case "Agendamento":
      return UnimedColors.greenLight6;
    default:
      return UnimedColors.redLight;
  }
}

Color protocolTypeColor(String protocolType) {
  switch (protocolType.trim()) {
    case "Autorização":
      return UnimedColors.creamDark;
    case "Agendamento":
      return UnimedColors.greenDark3;
    default:
      return UnimedColors.redDark;
  }
}

Color statusColor(String status) {
  switch (status.trim()) {
    case "Em andamento":
      return UnimedColors.yellowDark;
    case "Solicitado":
      return UnimedColors.yellowDark;
    case "Com pendências":
      return UnimedColors.red;
    case "Autorizado":
      return UnimedColors.greenLight7;
    default:
      return UnimedColors.blackText;
  }
}
