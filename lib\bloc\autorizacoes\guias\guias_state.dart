part of 'guias_bloc.dart';

abstract class GuiasState extends Equatable {
  const GuiasState();

  @override
  List<Object> get props => [];
}

class GuiasInitial extends GuiasState {}

class ErrorCarregarGuiaState extends GuiasState {
  final String message;

  ErrorCarregarGuiaState({required this.message});
}

class SuccessCarregarGuiaState extends GuiasState {
  final List<GuiaModel> list;
  final Map<String?, List<GuiaModel>> listStatus;

  SuccessCarregarGuiaState({required this.list, required this.listStatus});
}

class LoadingCarregarGuiaState extends GuiasState {}

class NoDataGuiaState extends GuiasState {}

class NoDataFoundGuiaState extends GuiasState {}


