import 'dart:io';

import 'package:cliente_minha_unimed/bloc/autorizacoes/eva-wpp/eva_wpp_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol.model.dart';

import 'package:cliente_minha_unimed/shared/i18n/i18n_helper.dart';
import 'package:cliente_minha_unimed/shared/utils/url_laucher.utils.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class AttendanceButton extends StatelessWidget {
  final BeneficiaryProtocolModel beneficiaryProtocolModel;

  AttendanceButton({required this.beneficiaryProtocolModel});

  final String baseTranslate = 'autorizations.v2.card.buttons';

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<EvaWppBloc, EvaWppState>(
      listener: (context, state) {
        if (state is DoneState) {
          String message = state.response.handleDataMessage(
              beneficiaryProtocolModel
                  .ansProtocolNumberHistory.ansProtocolNumber,
              BlocProvider.of<PerfilBloc>(context)
                      .perfil
                      .carteira
                      ?.carteiraNumero ??
                  "");

          //Feita separacao das url pois o launchUrl no Android nao funciona com a url https://wa.me

          final iOSURL = "https://wa.me/${state.response.phone}?text=$message";

          final androidURL =
              "whatsapp://send?phone=${state.response.phone}&text=$message";

          if (Platform.isIOS) {
            UrlLaucherUtils.launchURL(iOSURL);
          } else {
            UrlLaucherUtils.launchURL(androidURL);
          }
        } else if (state is ErrorState) {
          Alert.open(context,
              title: "Error", text: "Erro ao redirecionar para atendente");
        }
      },
      builder: (context, state) {
        return Row(
          children: [
            Expanded(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: UnimedColors.redLight2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    side: BorderSide(
                        color: beneficiaryProtocolModel.isFinished
                            ? UnimedColors.greyDisabled
                            : UnimedColors.redLight3)),
                onPressed: beneficiaryProtocolModel.isFinished
                    ? null
                    : () {
                        if (!(state is LoadingEvaWppState)) {
                          BlocProvider.of<EvaWppBloc>(context).add(CheckTalkEva(
                              perfil:
                                  BlocProvider.of<PerfilBloc>(context).perfil));
                        }
                      },
                child: Padding(
                  padding: const EdgeInsets.only(
                      top: 8.0, bottom: 8.0, left: 24.0, right: 24.0),
                  child: state is LoadingEvaWppState
                      ? SpinKitThreeBounce(color: UnimedColors.brow, size: 16)
                      : Text(
                          I18nHelper.translate(
                              context, '$baseTranslate.attendance'),
                          style: TextStyle(
                            color: beneficiaryProtocolModel.isFinished
                                ? UnimedColors.grayDark
                                : UnimedColors.brow,
                          )),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
