part of 'checkin_sympthons_pains_bloc.dart';

abstract class CheckinPageSympthonsPainsState extends Equatable {
  const CheckinPageSympthonsPainsState();

  @override
  List<Object?> get props => [];
}

class CheckinPageSympthonsPainsInitial extends CheckinPageSympthonsPainsState {}

class LoadingCheckinPageSympthonsPainsState
    extends CheckinPageSympthonsPainsState {}

class LoadingProcessTelecosultationState
    extends CheckinPageSympthonsPainsState {
  final int? step;

  @override
  List<Object?> get props => [step];

  LoadingProcessTelecosultationState({this.step});
}

class ShowPainsState extends CheckinPageSympthonsPainsState {}

class ErrorCheckinPageSympthonsPainsState
    extends CheckinPageSympthonsPainsState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorCheckinPageSympthonsPainsState(this.message);
}

class LoadedCheckinPageSympthonsPainsState
    extends CheckinPageSympthonsPainsState {
  final VEServiceModel? veServiceModel;

  @override
  List<Object?> get props => [veServiceModel];

  LoadedCheckinPageSympthonsPainsState({this.veServiceModel});
}
