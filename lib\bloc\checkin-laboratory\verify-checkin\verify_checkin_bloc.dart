import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-lab/checkinLab.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-lab/checkin-lab.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'verify_checkin_event.dart';
part 'verify_checkin_state.dart';

class VerifyCheckinBloc extends Bloc<VerifyCheckinEvent, VerifyCheckinState> {
  VerifyCheckinBloc() : super(VerifyCheckinInitial());

  String _id = "";
  String get id => _id;
  @override
  Stream<VerifyCheckinState> mapEventToState(VerifyCheckinEvent event) async* {
    if (event is VerifyCurrentCheckin) {
      yield LoadingVerifyCheckinState();

      try {
        ResultReturn _result = await Locator.instance
            .get<CheckinLabApi>()
            .verifyCurrentCheckin(perfil: event.perfil);
        _id = _result.id;
        if (_result.checkinResult == CheckinResultStatus.existing) {
          yield ExistingCheckinState(
            id: _result.id,
            expiredAt: _result.expirationAt ?? "",
          );
        } else if (_result.checkinResult == CheckinResultStatus.qrCodeExpired) {
          yield QrcodeExpiredState();
        } else if (_result.checkinResult == CheckinResultStatus.dontExist) {
          yield DontExistCheckinState();
        }
      } catch (ex) {
        yield ErrorVerifyCheckinState(message: ex.toString());
      }
    } else if (event is SendCheckinEvent) {
      yield LoadingSendCheckinState();
      try {
        final idQrCode =
            await Locator.instance.get<CheckinLabApi>().sendInfoQrCode(
                  user: event.user,
                  perfil: event.perfil,
                  checkinLabModel: event.checkinLabModel,
                );

        yield SuccessSendCheckinState(
          qrCode: base64Encode(utf8.encode(jsonEncode(idQrCode))),
        );
      } catch (ex) {
        yield ErrorSendCheckinState(message: ex.toString());
      }
    }
  }
}
