part of 'eva_wpp_bloc.dart';

abstract class EvaWppState extends Equatable {
  const EvaWppState();

  @override
  List<Object> get props => [];
}

class EvaWppInitial extends EvaWppState {}

class LoadingEvaWppState extends EvaWppState {
  @override
  List<Object> get props => [];
}

class ErrorState extends EvaWppState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorState(this.message);
}

class DoneState extends EvaWppState {
  final ResponseCheckBtnEvaVO response;

  @override
  List<Object> get props => [response];

  DoneState(this.response);
}
