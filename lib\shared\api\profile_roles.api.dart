import 'dart:convert';

import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/permissions/login_permissions.model.dart';
import 'package:cliente_minha_unimed/models/permissions/profile_roles.model.dart';
import 'package:cliente_minha_unimed/models/virtual-emergency-service/vaccine-data.model.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/api/vo/eva-wpp.vo.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:collection/collection.dart';
import 'package:http_client/http_client.dart';

class ProfileRolesApi {
  final apiUrl = FlavorConfig.instance!.values.profilePermissions.url;
  UnimedHttpClient httpClient = UnimedHttpClient(logger: UnimedLogger());
  final logger = UnimedLogger(className: 'ProfileRolesApi');
  static const tokenKey = 'token';
  String? _token;

  Future<LoginPermissionsModel?> loginPermissions() async {
    try {
      final String url = '${apiUrl}permissions/loginPermissions';
      await _refreshToken();
      final Map<String, String> headers = {
        "Content-Type": "application/json",
        "Authorization": 'Bearer $_token',
      };

      final response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      logger.d('canLogin response.body ${response.body}');

      if (response.statusCode == 200) {
        final result = jsonDecode(response.body);
        logger.d('canLogin success $result');
        return LoginPermissionsModel.fromJson(result);
      } else {
        logger.e('canLogin error status != 200 ${response.body}');
        return LoginPermissionsModel(canSignIn: false, canSignUp: false);
      }
    } on ServiceTimeoutException catch (e) {
      logger.e('Error on canLogin method ServiceTimeoutException ${e.message}');
      throw AuthException(e.message);
    } on AuthException catch (e) {
      logger.e('Error on canLogin method AuthException => $e');
      throw e;
    } catch (e) {
      logger.e('canLogin Exception $e');
      throw AuthException(e.toString());
    }
    // }
  }

  Future<ProfileRolesModel?> getProfilePermissions(String? cardId,
      List<ProfileRolesModel>? defaultPermissionList, String version) async {
    try {
      String url = '${apiUrl}v2/permissions/byCardId/$cardId';
      url = '$url?v=$version';

      await _refreshToken();
      final Map<String, String> headers = {
        "Content-Type": "application/json",
        "Authorization": 'Bearer $_token',
      };

      final response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        logger.d('getProfilePermissions success ${jsonDecode(response.body)}');
        return ProfileRolesModel.fromJson(jsonDecode(response.body));
      } else {
        logger.e('getProfilePermissions error status != 200 ${response.body}');
        return getDefaultPermissions(cardId, defaultPermissionList);
      }
    } catch (e) {
      logger.e('getProfilePermissions error $e');
      return getDefaultPermissions(cardId, defaultPermissionList);
    }
    // }
  }

  ProfileRolesModel? getDefaultPermissions(
      String? cardId, List<ProfileRolesModel>? defaultPermissionList) {
    if (defaultPermissionList != null) {
      return defaultPermissionList
          .firstWhereOrNull((element) => element.cardId == cardId);
    }
    return null;
  }

  Future<List<ProfileRolesModel>> getListProfilePermissions(
      List<String> cardIdList, String version) async {
    try {
      // final String url = apiUrl + 'permissions/byCardList';
      String url = apiUrl! + 'v2/permissions/byCardList';
      url = '$url?v=$version';
      final body = jsonEncode(cardIdList);
      await _refreshToken();
      final Map<String, String> headers = {
        "Content-Type": "application/json",
        "Authorization": 'Bearer $_token',
      };

      final response = await this
          .httpClient
          .post(Uri.parse(url), headers: headers, body: body);

      if (response.statusCode == 200) {
        final decodeBody = jsonDecode(response.body);
        final res = _buildListProfilePermissions(decodeBody);
        logger.i(
            'getListProfilePermissions success - List length: ${res.length}');
        return res;
      } else {
        logger.e('getListProfilePermissions error ${response.body}');
        throw ProfilesException('Falha ao carregar permissões do perfil');
      }
    } on NoInternetException catch (e) {
      logger.e('getListProfilePermissions error NoInternetException $e');
      throw NoInternetException();
    } on ServiceTimeoutException catch (e) {
      logger.e('getListProfilePermissions error ServiceTimeoutException $e');
      throw ServiceTimeoutException();
    } catch (e) {
      logger.e('getListProfilePermissions error $e');
      throw ProfilesException('Falha ao carregar permissões do perfil');
    }
    // }
  }

  Future<void> _refreshToken() async {
    _token = await Locator.instance.get<AuthApi>().tokenPerfilApps();
  }

  List<ProfileRolesModel> _buildListProfilePermissions(List data) {
    final List<ProfileRolesModel> result = [];
    for (final item in data) {
      result.add(ProfileRolesModel.fromJson(item));
    }

    return result;
  }

  Future<List<VaccineDataModel>> getListVaccines() async {
    try {
      final String url = '${apiUrl}virtualemergencyservice/vaccine';
      // final token = Locator.instance.get<ProfileRolesApi>().token;
      final Map<String, String> headers = {
        "Content-Type": "application/json",
        "Authorization": 'Bearer $_token',
      };

      final response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        final _list = (jsonDecode(response.body) as List)
            .map((obj) => VaccineDataModel.fromJson(obj))
            .toList();

        logger.d('getListVaccines success - List length: ${_list.length}');

        return _list;
      } else {
        logger.e(
            'virtual emergency statusCode: ${response.statusCode} - ${response.body}');
        throw UnimedException(jsonDecode(response.body)['message']);
      }
    } on NoInternetException catch (e) {
      logger.e('getListVaccines error NoInternetException $e');
      throw NoInternetException();
    } on ServiceTimeoutException catch (e) {
      logger.e('virtual emergency  getListVaccines ServiceTimeoutException $e');
      throw e;
    } on UnimedException catch (e) {
      logger.e('virtual emergency getListVaccines UnimedException $e');
      throw e;
    } on Exception catch (e) {
      logger.e('virtual emergency getListVaccines exception $e');
      throw GenericException();
    }
  }

  // @Deprecated('Removed after replace ProfileRoles to ProfileRoles2')
  // dynamic _getDefaultPermissionIPad(String card) {
  //   return {
  //     "cardId": card,
  //     "permissions": [
  //       {
  //         "_id": "5f7342d67ac77700115eda61",
  //         "roles": [
  //           {
  //             "_id": "5f7342d67ac77700115eda62",
  //             "key": "financial",
  //             "value": true
  //           },
  //           {
  //             "_id": "5f7342d67ac77700115eda63",
  //             "key": "appointment",
  //             "value": true
  //           },
  //           {
  //             "_id": "5f7342d67ac77700115eda64",
  //             "key": "authorizations",
  //             "value": true
  //           },
  //           {"_id": "5f7342d67ac77700115eda65", "key": "plan", "value": true},
  //           {
  //             "_id": "5f7342d67ac77700115eda66",
  //             "key": "virtualCard",
  //             "value": true
  //           },
  //           {
  //             "_id": "5f7342d67ac77700115eda67",
  //             "key": "optional",
  //             "value": true
  //           },
  //           {
  //             "_id": "5f7342d67ac77700115eda68",
  //             "key": "evaluation",
  //             "value": true
  //           },
  //           {
  //             "_id": "5f7342d67ac77700115eda69",
  //             "key": "virtualEmergencyService",
  //             "value": false
  //           },
  //           {"_id": "5f7342d67ac77700115eda6a", "key": "roberta", "value": true}
  //         ],
  //         "screen": "home"
  //       },
  //       {
  //         "_id": "5f7342d67ac77700115eda6b",
  //         "screen": "invoice",
  //         "roles": [
  //           {"_id": "5f7342d67ac77700115eda6c", "key": "opened", "value": true},
  //           {
  //             "_id": "5f7342d67ac77700115eda6d",
  //             "key": "toNegotiate",
  //             "value": true
  //           },
  //           {"_id": "5f7342d67ac77700115eda6e", "key": "status", "value": true}
  //         ]
  //       },
  //       {
  //         "_id": "5f7342d67ac77700115eda6f",
  //         "screen": "plan",
  //         "roles": [
  //           {
  //             "_id": "5f7342d67ac77700115eda70",
  //             "key": "agreement",
  //             "value": true
  //           },
  //           {"_id": "5f7342d67ac77700115eda71", "key": "use", "value": true},
  //           {
  //             "_id": "5f7342d67ac77700115eda72",
  //             "key": "coparticipation",
  //             "value": true
  //           }
  //         ]
  //       },
  //       {
  //         "_id": "5f7342d67ac77700115eda73",
  //         "screen": "request",
  //         "roles": [
  //           {
  //             "_id": "5f7342d67ac77700115eda74",
  //             "key": "newAuthorization",
  //             "value": true
  //           },
  //           {
  //             "_id": "5f7342d67ac77700115eda75",
  //             "key": "authorizations",
  //             "value": true
  //           },
  //           {
  //             "_id": "5f7342d67ac77700115eda76",
  //             "key": "eletronicGuide",
  //             "value": true
  //           }
  //         ]
  //       }
  //     ]
  //   };
  // }

  Future<ResponseCheckBtnEvaVO> checkTalkEvaWpp(
      {required Perfil perfil}) async {
    try {
      final _card = perfil.carteira!.carteiraNumero;
      final String url =
          '${apiUrl}solicitation/contact/whatsapp/$_card/card/permit';
      // final token = Locator.instance.get<ProfileRolesApi>().token;
      final token = await (Locator.instance.get<AuthApi>().tokenPerfilApps());

      final Map<String, String> headers = {
        "Content-Type": "application/json",
        "Authorization": 'Bearer $token',
      };

      final response =
          await this.httpClient.get(Uri.parse(url), headers: headers);

      if (response.statusCode == 200) {
        logger.d('checkTalkRobertaWpp success - body: ${response.body}');
        final _data = jsonDecode(response.body);
        final objResponse = ResponseCheckBtnEvaVO.fromJson(_data);

        return objResponse;
      } else if (response.statusCode == 401) {
        logger.e(
            'checkTalkRobertaWpp error statusCode: ${response.statusCode} - ${response.body}');
        throw ProfilesException(jsonDecode(response.body)['message']);
      } else {
        logger.e(
            'checkTalkRobertaWpp error statusCode: ${response.statusCode} - ${response.body}');
        throw GenericException();
      }
    } on NoInternetException catch (e) {
      logger.e('checkTalkRobertaWpp error NoInternetException $e');
      throw NoInternetException();
    } on ServiceTimeoutException catch (e) {
      logger.e('checkTalkRobertaWpp ServiceTimeoutException $e');
      throw e;
    } on ProfilesException catch (e) {
      logger.e('checkTalkRobertaWpp UnimedException $e');
      throw e;
    } on Exception catch (e) {
      logger.e('checkTalkRobertaWpp exception $e');
      throw GenericException();
    }
  }
}
