import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:cliente_minha_unimed/models/virtual-emergency-service/virtual-emergency-service.model.dart';

part 'checkin_page_sympthons_pains_event.dart';
part 'checkin_page_sympthons_pains_state.dart';

class CheckinPageSympthonsPainsBloc extends Bloc<CheckinPageSympthonsPainsEvent,
    CheckinPageSympthonsPainsState> {
  CheckinPageSympthonsPainsBloc() : super(CheckinPageSympthonsPainsInitial());

  String? _waitingTime;
  String? get waitingTime => _waitingTime;

  @override
  Stream<CheckinPageSympthonsPainsState> mapEventToState(
    CheckinPageSympthonsPainsEvent event,
  ) async* {
    if (event is CheckinDoneSympthons) {
      yield ShowPainsState();
    }
    if (event is CheckinBackToSympthons) {
      yield CheckinPageSympthonsPainsInitial();
    } else if (event is ResetCheckinAttendance) {
      _waitingTime = null;

      yield CheckinPageSympthonsPainsInitial();
    }
  }
}
