import 'dart:async';
import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';

import 'package:cliente_minha_unimed/shared/services/authorization.service.dart';

import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/verify_solic_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/verify-solic/verify_solic_state.dart';
import 'package:shared_preferences/shared_preferences.dart';

class VerifySolicBloc extends Bloc<VerifySolicEvent, VerifySolicState> {
  VerifySolicBloc() : super(VerifySolicInitial());

  @override
  Stream<VerifySolicState> mapEventToState(
    VerifySolicEvent event,
  ) async* {
    if (event is VerifyStatus) {
      yield VerifyingRequest();
      final status = await AuthorizationService.getStatusAuthorization(
        event.perfil!.contratoBeneficiario.carteira!.carteiraNumero,
      );

      if (status == 0) {
        final aut = await AuthorizationService.getAuthorizationData(
          event.perfil!.contratoBeneficiario.carteira!.carteiraNumero,
        );
        yield FailRequest(envioSolicitacaoAutorizacao: aut);
      } else if (status == 1) {
        yield LoadingRequest();
      } else {
        yield SuccessRequest();
      }
    }
  }

  Future<bool> isFirstAccess() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getBool(_prefsPath) ?? true;
  }

  Future<bool> setFirstAccess() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setBool(_prefsPath, false);
  }

  String get _prefsPath {
    final sufix = FlavorConfig.isProduction() ? '_prod' : '_dev';

    return 'solicitation_feature_discovery_$sufix';
  }
}
