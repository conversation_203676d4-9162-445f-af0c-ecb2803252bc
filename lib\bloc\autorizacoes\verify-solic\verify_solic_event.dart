import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';

@immutable
abstract class VerifySolicEvent extends Equatable {}

class VerifyRequest extends VerifySolicEvent {
  final String? notificationId;
  @override
  List<Object?> get props => [notificationId];
  VerifyRequest({this.notificationId});
}

class VerifyStatus extends VerifySolicEvent {
  final Perfil? perfil;
  @override
  List<Object?> get props => [perfil];

  VerifyStatus({required this.perfil});
}
