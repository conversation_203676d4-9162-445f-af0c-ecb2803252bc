import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/password_rules.model.dart';
import 'package:cliente_minha_unimed/shared/api/cadastro.api.dart';
import 'package:cliente_minha_unimed/shared/api/redefinir_senha.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/compare_date.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';

import 'cadastro_event.dart';
import 'cadastro_state.dart';

class CadastroEnvioBloc extends Bloc<CadastroEvent, CadastroEnvioState> {
  CadastroEnvioBloc() : super(InitialState());
  final logger = UnimedLogger(className: 'CadastroBloc');

  final List<PasswordRulesModel> rules = [];

  @override
  Stream<CadastroEnvioState> mapEventToState(
    CadastroEvent event,
  ) async* {
    if (event is ChangeCadastroEvent) {
      yield LoadingState();

      final api = Locator.instance.get<CadastroApi>();

      try {
        _validateForm(event.dao);
        final resultado = await api.postSignup(event.dao);
        yield DoneState(resultado);
      } catch (e) {
        // logger.e('BLOC $e');
        if (e is SocketException || e is HandshakeException)
          yield ErrorState(MessageException.GENERAL);
        if (e is SignupException) yield ErrorState(e.message);
        yield ErrorState("$e");
      }
    } else if (event is InicioCadastroEvent) {
      yield InitialState();
    } else if (event is GetPasswordRulesEvent) {
      yield LoadingRullesPasswordState();
      try {
        rules.clear();
        final apiRulesPassword = Locator.instance.get<RedefinirSenhaApi>();

        final List<PasswordRulesModel>? resultListPasswordRulesModel =
            await apiRulesPassword.getPasswordRules();

        rules.addAll(resultListPasswordRulesModel ?? []);

        yield DoneGetPasswordRulesState(rules: rules);
      } on UnimedException catch (e) {
        yield ErrorRulesState(e.message);
      } on Exception catch (e) {
        if (e is SocketException || e is HandshakeException)
          yield ErrorRulesState(
              "Não foi possível conectar, tente novamente mais tarde.");
      }
    }
  }

  void _validateForm(EnvioCadastro dao) {
    if (dao.cpf.isEmpty)
      throw SignupException('O campo cpf não pode ser vazio!');
    if (!StringUtils.validateCpf(dao.cpfWithoutMask))
      throw SignupException('Digite um cpf válido');
    if (dao.dataNascimento == null || dao.dataNascimentoStr.isEmpty)
      throw SignupException('O campo data de nascimento não pode ser vazio!');
    if (!CompareDate.isValidDate(dao.inputDate).first)
      throw SignupException(CompareDate.isValidDate(dao.inputDate)[1]);
    if (dao.email.isEmpty)
      throw SignupException('O campo email não pode ser vazio!');
    if (!StringUtils.validateEmail(dao.email))
      throw SignupException('Digite um e-mail válido');
    if (dao.telefone.isEmpty)
      throw SignupException('O campo telefone não pode ser vazio!');
    if (dao.senha.isEmpty)
      throw SignupException('O campo senha não pode ser vazio!');
    if (dao.senha.compareTo(dao.confirmaSenha) < 0)
      throw SignupException('Campo Senha deve ser igual ao Confirmar senha');
  }
}
