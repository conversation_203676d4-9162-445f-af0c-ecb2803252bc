import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:url_launcher/url_launcher.dart';

class UrlLaucherUtils {
  static UnimedLogger logger = UnimedLogger(className: 'UrlLaucherUtils');

  static Future launchURL(String url) async {
    logger.i('launchURL - abrindo url $url');
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } else {
      throw 'Unable to open url : $url';
    }
  }
}
