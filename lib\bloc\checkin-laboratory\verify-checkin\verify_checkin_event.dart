part of 'verify_checkin_bloc.dart';

abstract class VerifyCheckinEvent extends Equatable {
  const VerifyCheckinEvent();

  @override
  List<Object> get props => [];
}

class VerifyCurrentCheckin extends VerifyCheckinEvent {
  final Perfil perfil;

  VerifyCurrentCheckin({required this.perfil});
}

class SendCheckinEvent extends VerifyCheckinEvent {
  final Perfil perfil;
  final User user;
  final CheckinLabModel checkinLabModel;

  @override
  List<Object> get props => [perfil, user, checkinLabModel];

  SendCheckinEvent(
      {required this.perfil,
      required this.user,
      required this.checkinLabModel});
}


