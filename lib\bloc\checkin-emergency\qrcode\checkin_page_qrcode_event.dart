part of 'checkin_page_qrcode_bloc.dart';

abstract class CheckinQrCodeEvent extends Equatable {
  const CheckinQrCodeEvent();

  @override
  List<Object?> get props => [];
}

class VerifyTimePA extends CheckinQr<PERSON>odeEvent {
  final Carteira carteira;

  @override
  List<Object> get props => [carteira];

  VerifyTimePA(this.carteira);
}

class GetQRCodeDataEvent extends CheckinQrCodeEvent {
  final Perfil? perfil;
  final User? user;
  final CheckinDataModel? checkinDataModel;
  final PA? paConfig;

  @override
  List<Object?> get props => [perfil, user, checkinDataModel, paConfig];

  GetQRCodeDataEvent(
      {this.perfil, this.user, this.checkinDataModel, this.paConfig});
}
