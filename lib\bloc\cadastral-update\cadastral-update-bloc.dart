import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/indica-adesao-notificacao.vo.dart';
import 'package:equatable/equatable.dart';

import 'package:cliente_minha_unimed/models/virtual-emergency-service/virtual-emergency-service.model.dart';

part 'cadastral-update-event.dart';
part 'cadastral-update-state.dart';

class CadastralUpdateBloc extends Bloc<CadastralUpdateEvent, int> {
  CadastralUpdateBloc() : super(0);

  int _pageIndex = 0;
  int get pageIndex => _pageIndex;

  late String _selectedType;
  String get selectedType => _selectedType;
  set setSelectedType(String type) {
    _selectedType = type;
  }

  String _email = "";
  String get email => _email;

  String _data = "";
  String get data => _data;

  late IndicaAdesaoNotificacaoVO indicaAdesaoNotificacaoVO;

  @override
  Stream<int> mapEventToState(
    CadastralUpdateEvent event,
  ) async* {
    if (event is AdvanceStepCadastralUpdateEvent) {
      _pageIndex++;

      yield _pageIndex;
    } else if (event is BackStepCadastralUpdateEvent) {
      _pageIndex--;

      yield _pageIndex;
    } else if (event is UpdateDataCadastralUpdate) {
      _data = event.data;
      _email = event.email;

      _pageIndex++;
      yield _pageIndex;
    }
  }
}
