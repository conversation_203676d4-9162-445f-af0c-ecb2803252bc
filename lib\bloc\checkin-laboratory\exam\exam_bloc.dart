import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/models/checkin-lab/exam.model.dart';
import 'package:cliente_minha_unimed/models/checkin-lab/guide.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/shared/api/checkin/checkin-lab/guide.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:equatable/equatable.dart';

part 'exam_event.dart';
part 'exam_state.dart';

class ExamGuideBloc extends Bloc<ExamEvent, ExamState> {
  ExamGuideBloc() : super(ExamInitial());

  @override
  Stream<ExamState> mapEventToState(
    ExamEvent event,
  ) async* {
    if (event is LoadListExam) {
      yield LoadingListExam();
      try {
        List<GuideExamsModel> _list = [];
        for (GuideModel guide in event.guideModel) {
          // final _exams = await Locator.instance!.get<GuideApi>().getExamGuide(
          //     perfil: event.perfil,
          //     checkinLabConfig: event.checkinLabConfig,
          //     guideModel: guide);

          _list.add(GuideExamsModel(
            exams: await Locator.instance.get<GuideApi>().getExamGuide(
                  perfil: event.perfil,
                  checkinLabConfig: event.checkinLabConfig,
                  guideModel: guide,
                ),
            guideModel: guide,
          ));
        }

        yield SuccessListExam(list: _list);
      } on GuideException catch (ex) {
        yield ErrorListExam(message: '${ex.message}');
      } catch (ex) {
        yield ErrorListExam(message: '$ex');
      }
    }
  }
}
