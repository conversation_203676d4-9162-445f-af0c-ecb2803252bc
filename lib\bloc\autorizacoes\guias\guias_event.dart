part of 'guias_bloc.dart';

abstract class GuiasEvent extends Equatable {
  const GuiasEvent();

  @override
  List<Object> get props => [];
}

class CarregarGuiasEvent extends GuiasEvent {
  final Perfil perfil;
  final DateTime? dataInicio;
  final DateTime? dataFim;

  CarregarGuiasEvent({required this.perfil, this.dataInicio, this.dataFim});
}

class GuiasFiltro extends GuiasEvent {
  final String? filter;
  final DateTime? dateFilter;

  @override
  List<Object> get props => [filter ?? ''];

  GuiasFiltro({this.filter, this.dateFilter});
}


