part of 'checkin_page_info_bloc.dart';

abstract class CheckinInfoEvent extends Equatable {
  const CheckinInfoEvent();

  @override
  List<Object?> get props => [];
}

class VerifyTimePA extends CheckinInfoEvent {
  final Carteira carteira;

  @override
  List<Object> get props => [carteira];

  VerifyTimePA(this.carteira);
}

class GetInfosCheckin extends CheckinInfoEvent {
  final Perfil? perfil;
  final User? user;

  @override
  List<Object?> get props => [perfil, user];

  GetInfosCheckin({this.perfil, this.user});
}
