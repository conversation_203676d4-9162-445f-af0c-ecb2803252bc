import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_state.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/exam/exam_event.dart';
import 'package:cliente_minha_unimed/shared/api/autorizacoes.api.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/file.dart';

class PdfExamBloc extends Bloc<ExamEvent, ExamState> {
  PdfExamBloc() : super(ExamInitial());

  @override
  Stream<ExamState> mapEventToState(
    ExamEvent event,
  ) async* {
    if (event is GeneratePdf) {
      yield LoadingPdf();
      try {
        final api = Locator.instance.get<AutorizacoesApi>();
        final _path = await api.getPdfSolic(
            perfil: event.perfil!, codSolicitacao: event.cod);
        final _pathFile = await FileUtils.createFileFromString(
            base64String: _path, extension: FileExtension.PDF);
        yield DonePdf(path: _pathFile, codSolicitacao: event.cod);
      } catch (ex) {
        yield ErrorPdf('$ex');
      }
    }
  }
}
