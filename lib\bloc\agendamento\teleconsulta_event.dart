import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/medical-guide/provider.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/shared/api/vo/agendamento/agendamento.vo.dart';

import 'package:cliente_minha_unimed/models/agenda-agendamento.model.dart';

@immutable
abstract class TeleconsultaEvent extends Equatable {}

class ListAllAgendasEvent extends TeleconsultaEvent {
  final ProviderModel? prestador;
  final Perfil? perfil;
  final bool isTeleconsulta;
  final ProviderModel? clinicaUnimed;
  final EspecialtyModel? especialidade;
  final DateTime? dataInicio;
  final DateTime? dataFim;

  @override
  List<Object?> get props => [
        prestador,
        perfil,
        isTeleconsulta,
        clinicaUnimed,
        especialidade,
        dataInicio,
        dataFim,
      ];

  ListAllAgendasEvent({
    required this.prestador,
    required this.perfil,
    required this.isTeleconsulta,
    this.especialidade,
    this.clinicaUnimed,
    this.dataInicio,
    this.dataFim,
  });
}

class AutorizarEvent extends TeleconsultaEvent {
  final AgendamentoVO agendamento;
  final Perfil perfil;

  @override
  List<Object> get props => [agendamento, perfil];

  AutorizarEvent({required this.agendamento, required this.perfil});
}

class ClearStateEvent extends TeleconsultaEvent {
  @override
  List<Object> get props => [];
}

class SelectEspecialty extends TeleconsultaEvent {
  final EspecialtyModel especialtyModel;

  @override
  List<Object?> get props => [especialtyModel];

  SelectEspecialty(this.especialtyModel);
}

class SelectHorario extends TeleconsultaEvent {
  final AgendaAgendamentoVO horario;

  @override
  List<Object?> get props => [horario];

  SelectHorario(this.horario);
}

class AdvanceStep extends TeleconsultaEvent {
  final int indexPage;

  @override
  List<Object?> get props => [indexPage];

  AdvanceStep(this.indexPage);
}

class BackStep extends TeleconsultaEvent {
  final int indexPage;

  @override
  List<Object?> get props => [indexPage];

  BackStep(this.indexPage);
}
