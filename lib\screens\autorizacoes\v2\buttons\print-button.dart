import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_bloc.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_event.dart';
import 'package:cliente_minha_unimed/bloc/autorizacoes/solicitacao/guide_solicitation_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/beneficiary-protocol.model.dart';

import 'package:cliente_minha_unimed/shared/i18n/i18n_helper.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/pdf-view/pdf-view-screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class PrintButton extends StatelessWidget {
  final BeneficiaryProtocolModel beneficiaryProtocolModel;

  PrintButton({required this.beneficiaryProtocolModel});

  final String baseTranslate = 'autorizations.v2.card.buttons';

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<GuideSolicitationBloc, GuideSolicitationState>(
      listener: (context, state) {
        if (state is DoneGetGuidesPdfState) {
          if (state.guidesPath.length == 1) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PDFViewScreen(
                  state.guidesPath.first,
                  title: 'Detalhes da guia',
                  isPath: true,
                ),
              ),
            );
          } else {
            _showGuidesModal(context, guidesPath: state.guidesPath);
          }
        } else if (state is EmptyGuidesPdfState) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              duration: Duration(seconds: 3),
              backgroundColor: UnimedColors.redStatus,
              content: Text(
                'Nenhuma guia disponível',
              ),
            ),
          );
        } else if (state is ErrorGuideSolicitationState) {
          Alert.open(
            context,
            title: 'Imprimir guia',
            text:
                "${beneficiaryProtocolModel.ansProtocolNumberHistory.ansProtocolNumber}\n\n${state.message}",
          );
        }
      },
      builder: (context, state) {
        return beneficiaryProtocolModel.isAuthorization
            ? Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                          backgroundColor: UnimedColors.greenLight9,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          side: BorderSide(color: UnimedColors.greenLight8)),
                      onPressed: () {
                        if (state is LoadingGuideSolicitationState) return;

                        BlocProvider.of<GuideSolicitationBloc>(context).add(
                          GetGuidesPdfV2(
                              numProtocolo: beneficiaryProtocolModel
                                  .ansProtocolNumberHistory.ansProtocolNumber,
                              numAtend: beneficiaryProtocolModel
                                  .ansProtocolNumberHistory
                                  .atendAuditPrevent
                                  ?.numAtend),
                        );
                      },
                      child: Padding(
                          padding: const EdgeInsets.only(
                              top: 8.0, bottom: 8.0, left: 24.0, right: 24.0),
                          child: state is LoadingGuideSolicitationState
                              ? SpinKitThreeBounce(
                                  color: UnimedColors.greenDark5,
                                  size: 16,
                                )
                              : Text(
                                  I18nHelper.translate(
                                      context, '$baseTranslate.print'),
                                  style: TextStyle(
                                    color: UnimedColors.greenDark5,
                                  ),
                                )),
                    ),
                  ),
                ],
              )
            : Container();
      },
    );
  }

  void _showGuidesModal(BuildContext context,
      {required List<String> guidesPath}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return Container(
          padding: const EdgeInsets.fromLTRB(24.0, 16.0, 24.0, 32.0),
          color: UnimedColors.white,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(mainAxisAlignment: MainAxisAlignment.end, children: [
                  IconButton(
                    icon: Icon(Icons.close, color: UnimedColors.green),
                    onPressed: () {
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);
                      }
                    },
                  )
                ]),
                const SizedBox(
                  height: 10,
                ),
                for (var index = 0; index < guidesPath.length; index++)
                  _cardFile(context, pathFile: guidesPath[index], index: index),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _cardFile(BuildContext context,
      {required String pathFile, required int index}) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PDFViewScreen(
              pathFile,
              title: 'Detalhes da guia',
              isPath: true,
            ),
          ),
        );
      },
      child: Container(
        // height: 100,
        width: MediaQuery.of(context).size.width,
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16.0),
        margin: const EdgeInsets.only(bottom: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey,
              offset: Offset(0.0, 1.0), //(x,y)
              blurRadius: 6.0,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  Icon(Icons.picture_as_pdf_outlined, size: 45),
                  const SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    child: Text('Arquivo ${index + 1}'),
                  ),
                ],
              ),
            ),
            Icon(Icons.chevron_right_rounded, size: 30)
          ],
        ),
      ),
    );
  }
}
