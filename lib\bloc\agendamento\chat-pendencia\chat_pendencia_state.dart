import 'package:equatable/equatable.dart';
import 'package:meta/meta.dart';
import 'package:cliente_minha_unimed/models/pendencia-agendamento.dart';

@immutable
abstract class ChatPendenciaState extends Equatable {}

class InitialChatPendenciaState extends ChatPendenciaState {
  @override
  List<Object> get props => [];
}

class LoadingListarChatState extends ChatPendenciaState {
  @override
  List<Object> get props => [];
}

class ErrorListarChatState extends ChatPendenciaState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorListarChatState(this.message);
}

class DoneListarChatState extends ChatPendenciaState {
  final List<RetornoListChat>? listRetorno;

  @override
  List<Object?> get props => [listRetorno];

  DoneListarChatState({this.listRetorno});
}

class LoadingSalvarChatState extends ChatPendenciaState {
  @override
  List<Object> get props => [];
}

class ErrorSalvarChatState extends ChatPendenciaState {
  final String message;

  @override
  List<Object> get props => [message];

  ErrorSalvarChatState(this.message);
}

class DoneSalvarChatState extends ChatPendenciaState {
  final String? message;

  DoneSalvarChatState(this.message);
  @override
  List<Object?> get props => [message];
}
