part of 'checkin_emergency_bloc.dart';

abstract class CheckinEmergencyEvent {
  const CheckinEmergencyEvent();
}

class NextPage extends CheckinEmergencyEvent {}

class BackPage extends CheckinEmergencyEvent {}

class SetToInitialState extends CheckinEmergencyEvent {}

class GoToPage extends CheckinEmergencyEvent {
  final int index;

  GoToPage({required this.index});
}

class GoToLastPage extends CheckinEmergencyEvent {

  GoToLastPage();
}

class NewCheckin extends CheckinEmergencyEvent {
  final Perfil? perfil;

  NewCheckin({this.perfil});
}

class Create<PERSON><PERSON>ckin extends CheckinEmergencyEvent {
  final CheckinDataModel? checkinDataModel;
  final Perfil? perfil;

  Create<PERSON><PERSON>ckin({this.checkinDataModel, this.perfil});
}
